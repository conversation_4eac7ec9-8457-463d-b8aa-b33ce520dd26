<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>charger-app</artifactId>
        <groupId>com.platform</groupId>
        <version>4.9.0</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>
    <packaging>jar</packaging>
    <artifactId>charger-app-api</artifactId>
    <description>通用app服务公共api模块</description>

    <properties>
        <maven.compiler.source>8</maven.compiler.source>
        <maven.compiler.target>8</maven.compiler.target>
    </properties>
    <dependencies>
        <!--core 工具类-->
        <dependency>
            <groupId>com.platform</groupId>
            <artifactId>platform-common-core</artifactId>
            <version>4.9.0</version>
        </dependency>
        <!--feign 依赖-->
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-openfeign</artifactId>
        </dependency>
        <!--断路器依赖-->
        <dependency>
            <groupId>com.platform</groupId>
            <artifactId>platform-common-feign</artifactId>
            <version>4.9.0</version>
        </dependency>
        <dependency>
            <groupId>com.platform</groupId>
            <artifactId>charger-business-api</artifactId>
            <version>4.9.0</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.platform</groupId>
            <artifactId>charger-fleets-api</artifactId>
            <version>4.9.0</version>
            <scope>compile</scope>
        </dependency>
    </dependencies>
</project>