package com.platform.charger.app.api.feign;

import com.platform.charger.app.api.entity.*;
import com.platform.charger.app.api.feign.factory.RemoteAppServiceFallBackFactory;
import com.platform.charger.business.api.entity.ChargerAppCreditPayEntity;
import com.platform.platform.common.core.constant.SecurityConstants;
import com.platform.platform.common.core.constant.ServiceNameConstants;
import com.platform.platform.common.core.util.R;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestParam;

import javax.validation.Valid;
import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @title: RemoteFileUploadService
 * @projectName Platform20190712
 * @description: TODO
 * @date 2019/7/3117:02
 */
@FeignClient(contextId = "remoteAppService", value = ServiceNameConstants.APP_SERVICE, fallbackFactory = RemoteAppServiceFallBackFactory.class)
public interface RemoteAppService {

    /**
     *
     * @param shortId 用户短ID，以M为前缀
     * @param from
     * @return
     */
    @PostMapping("/remote/getUserByShortIdIncludeBlock")
    R<TUser> getUserByShortIdIncludeBlock(@RequestParam("shortId") String shortId, @RequestHeader(SecurityConstants.FROM) String from);

    /**
     *
     * @param id 用户ID
     * @param from
     * @return
     */
    @PostMapping("/remote/getUserById")
    R<TUser> getUserById(@RequestParam("id") Integer id, @RequestHeader(SecurityConstants.FROM) String from);

    /**
     * 删除站点时，更新用户收藏站点表
     * @param stationId 删除站点ID
     */
    @PostMapping("/remote/delStation")
    R<Boolean> delStation(@RequestParam("stationId") Integer stationId, @RequestHeader(SecurityConstants.FROM) String from);

    /**
     * 用户停止充电后的订单支付
     * @param orderEntity 停止充电后，需结算的订单数据
     * @return
     */
    /*@PostMapping("/remote/payOrder")
    R payOrder(@RequestBody ChargerOrderEntity orderEntity, @RequestHeader(SecurityConstants.FROM) String from);*/

    /**
     * 获得运营用户统计数据
     */
    @PostMapping("/remote/getAppUserStatistics")
    R<AppUserStatisticsVo> getAppUserStatistics(@RequestBody QueryAppUserListDto dto, @RequestHeader(SecurityConstants.FROM) String from);

    /**
     * 用户支付预约订单
     */
    /*@PostMapping("/remote/payReserveOrder")
    R payReserveOrder(@RequestBody ChargerReserveOrderEntity orderEntity, @RequestHeader(SecurityConstants.FROM) String from);*/

    /**
     *删除appUser卡号
     */
    @PostMapping("/remote/delCard")
    R delCard(@RequestParam("userId")Integer userId,@RequestParam("cardSn")String cardSn,@RequestHeader(SecurityConstants.FROM)String from);

    /**
     * 保存用户订单统计数据
     */
    @PostMapping("/remote/saveAppChargerOrderStatistics")
    void saveAppChargerOrderStatistics(@RequestBody List<TAppOrderStatistics> list, @RequestHeader(SecurityConstants.FROM) String from);

    @PostMapping("/remote/updateCardStatus")
    void updateCardStatus(@RequestParam("cardSn")String cardSn,@RequestParam("status")Integer status, @RequestHeader(SecurityConstants.FROM) String from);

    @PostMapping("/remote/updateCardBalance")
    void updateCardBalance(@RequestParam("cardSn")String cardSn, @RequestParam("balance") BigDecimal balance, @RequestHeader(SecurityConstants.FROM) String from);

    @PostMapping("/remote/updateCardType")
    void updateCardType(@RequestParam("cardSn")String cardSn, @RequestParam("type") Integer type, @RequestHeader(SecurityConstants.FROM) String from);

    /**
     * 向APP用户推送通知
     */
    @PostMapping("/remote/sendNotifyToUser")
    void sendNotifyToUser(@RequestBody ChargerAppNotifyEntity entity, @RequestHeader(SecurityConstants.FROM) String from);

    @PostMapping("/remote/fleetsBindApp")
    R<TUser> fleetsBindApp(@Valid @RequestBody FleetsBindAppDto dto, @RequestHeader(SecurityConstants.FROM) String from);

    @PostMapping("/remote/fleetsDelDriver")
    R fleetsDelDriver(@RequestParam("driverId") Integer driverId, @RequestHeader(SecurityConstants.FROM) String from);

    @PostMapping("/remote/getUserByEmail")
    R<TUser> getUserByEmail(@RequestParam("email")String email, @RequestParam("uuid") String payUuid, @RequestHeader(SecurityConstants.FROM)String from);

	@PostMapping("/remote/creditPay")
	R creditPay(@RequestBody ChargerAppCreditPayEntity entity, @RequestHeader(SecurityConstants.FROM)String from);

	@PostMapping("/remote/checkCredit")
	R checkCredit(@RequestParam("userId") Integer userId, @RequestHeader(SecurityConstants.FROM)String from);
}
