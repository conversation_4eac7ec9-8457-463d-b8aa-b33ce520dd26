package com.platform.charger.app.api.feign.fallback;

import com.platform.charger.app.api.entity.*;
import com.platform.charger.app.api.feign.RemoteAppService;
import com.platform.charger.business.api.entity.ChargerAppCreditPayEntity;
import com.platform.platform.common.core.constant.enums.ResponseTypeEnum;
import com.platform.platform.common.core.util.R;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @title: RemoteFileUploadServiceFallBackImpl
 * @projectName Platform20190712
 * @description: TODO
 * @date 2019/7/3117:03
 */
@Slf4j
@Component
public class RemoteAppServiceFallBackImpl implements RemoteAppService {
	@Setter
	private Throwable cause;

	@Override
	public R<TUser> getUserByShortIdIncludeBlock(String shortId, String from) {
		log.error("feign-远程调用查询App用户信息失败:{}", cause);
		return R.ok(ResponseTypeEnum.GLOBAL_REQUEST_FAILED);
	}

	@Override
	public R<TUser> getUserById(Integer id, String from) {
		log.error("feign-远程调用id查询App用户信息失败:{}", cause);
		return R.ok(ResponseTypeEnum.GLOBAL_REQUEST_FAILED);
	}

	@Override
	public R delStation(Integer stationId, String from) {
		log.error("feign-远程更新用户收藏站点信息失败:{}", cause);
		return R.ok(ResponseTypeEnum.GLOBAL_REQUEST_FAILED);
	}

	/*@Override
	public R payOrder(ChargerOrderEntity orderEntity, String from) {
		log.error("feign-用户支付充电订单失败:{}", cause);
		return R.ok(ResponseTypeEnum.GLOBAL_REQUEST_FAILED);
	}*/

	@Override
	public R getAppUserStatistics(QueryAppUserListDto dto, String from) {
		log.error("feign-获取运营用户统计信息失败:{}", cause);
		return R.ok(ResponseTypeEnum.GLOBAL_REQUEST_FAILED);
	}

	/*@Override
	public R payReserveOrder(ChargerReserveOrderEntity orderEntity, String from) {
		log.error("feign-用户支付预约订单失败:{}", cause);
		return R.ok(ResponseTypeEnum.GLOBAL_REQUEST_FAILED);
	}*/

	@Override
	public R delCard(Integer userId, String cardSn,String from) {
		log.error("feign-删除appUser卡号失败:{}", cause);
		return R.ok(ResponseTypeEnum.GLOBAL_REQUEST_FAILED);
	}

	@Override
	public void saveAppChargerOrderStatistics(List<TAppOrderStatistics> list, String from) {
		log.error("feign-App用户订单统计失败:{}", cause);
	}

	@Override
	public void updateCardStatus(String cardSn, Integer status, String from) {
		log.error("feign-App卡状态更新失败:{}", cause);
	}

	@Override
	public void updateCardBalance(String cardSn, BigDecimal balance, String from) {
		log.error("feign-App卡余额失败:{}", cause);
	}

	@Override
	public void updateCardType(String cardSn, Integer type, String from) {
		log.error("feign-App卡类型失败:{}", cause);
	}

	@Override
	public void sendNotifyToUser(ChargerAppNotifyEntity entity, String from) {
		log.error("feign-向App用户推送通知失败:{}", cause);
	}

	@Override
	public R fleetsBindApp(FleetsBindAppDto dto, String from) {
		log.error("feign-车队添加用户失败:{}", cause);
		return R.ok(ResponseTypeEnum.GLOBAL_REQUEST_FAILED);
	}

	@Override
	public R fleetsDelDriver(Integer driverId, String from) {
		log.error("feign-fleets解绑appUser失败:{}", cause);
		return R.ok(ResponseTypeEnum.GLOBAL_REQUEST_FAILED);
	}

	@Override
	public R<TUser> getUserByEmail(String email, String payUuid, String from) {
		log.error("feign-邮箱查询用户失败:{}", cause);
		return R.ok(ResponseTypeEnum.GLOBAL_REQUEST_FAILED);
	}

	@Override
	public R creditPay(ChargerAppCreditPayEntity entity, String from) {
		log.error("feign-信用卡扣费失败:{}", cause);
		return R.ok(ResponseTypeEnum.GLOBAL_REQUEST_FAILED);
	}

	@Override
	public R checkCredit(Integer userId, String from) {
		log.error("feign-信用卡查询失败:{}", cause);
		return R.ok(ResponseTypeEnum.GLOBAL_REQUEST_FAILED);
	}
}
