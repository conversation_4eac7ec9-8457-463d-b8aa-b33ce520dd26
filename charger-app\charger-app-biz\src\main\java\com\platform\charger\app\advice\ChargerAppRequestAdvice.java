package com.platform.charger.app.advice;

import com.alibaba.fastjson.JSON;
import com.platform.charger.app.annotation.ChargerApp;
import com.platform.charger.app.service.ChargerRedisService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.IOUtils;
import org.springframework.core.MethodParameter;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpInputMessage;
import org.springframework.http.converter.HttpMessageConverter;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.servlet.HandlerInterceptor;
import org.springframework.web.servlet.mvc.method.annotation.RequestBodyAdvice;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.lang.reflect.Type;
import java.util.List;
import java.util.Map;

@Slf4j
@RestControllerAdvice
@AllArgsConstructor
public class ChargerAppRequestAdvice implements HandlerInterceptor, RequestBodyAdvice {
    private final ChargerRedisService chargerRedisService;

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler)
            throws Exception {
        return Boolean.TRUE;
    }

    @Override
    public boolean supports(MethodParameter methodParameter, Type type, Class<? extends HttpMessageConverter<?>> aClass) {
        return methodParameter.hasMethodAnnotation(ChargerApp.class);
    }

    @Override
    public HttpInputMessage beforeBodyRead(HttpInputMessage httpInputMessage, MethodParameter methodParameter, Type type, Class<? extends HttpMessageConverter<?>> aClass) throws IOException {
        if (methodParameter.getMethodAnnotation(ChargerApp.class).filter()) {
            return new HttpInputMessage() {
                @Override
                public InputStream getBody() throws IOException {
                    Map<String, Object> map = (Map<String, Object>) JSON.parseObject(IOUtils.toString(httpInputMessage.getBody()), Map.class);
                    List<Long> list = chargerRedisService.getAgentIdsByUuid(httpInputMessage.getHeaders().getFirst("uuid"));
                    map.put("agentIds", list);
                    return IOUtils.toInputStream(JSON.toJSONString(map), "utf-8");
                }

                @Override
                public HttpHeaders getHeaders() {
                    return httpInputMessage.getHeaders();
                }
            };
        }


        return httpInputMessage;
    }

    @Override
    public Object afterBodyRead(Object o, HttpInputMessage httpInputMessage, MethodParameter methodParameter, Type type, Class<? extends HttpMessageConverter<?>> aClass) {
        return o;
    }

    @Override
    public Object handleEmptyBody(Object o, HttpInputMessage httpInputMessage, MethodParameter methodParameter, Type type, Class<? extends HttpMessageConverter<?>> aClass) {
        return null;
    }
}
