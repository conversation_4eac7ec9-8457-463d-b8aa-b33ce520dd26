package com.platform.charger.app.aspect;

import cn.hutool.core.util.ObjectUtil;
import com.platform.charger.app.annotation.SafeRead;
import com.platform.charger.app.api.entity.TUser;
import com.platform.platform.common.core.util.R;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.stereotype.Component;

import java.lang.reflect.Field;
import java.util.*;

@Component
@Aspect
@Slf4j
public class SafeReadAspect {
    private final String SAVE_READ_FIELD_APPID = "appId";
    private final String SAVE_READ_FIELD_USERID = "userId";

    @Around("@annotation(safeRead)")
    @SneakyThrows
    public Object around(ProceedingJoinPoint point, SafeRead safeRead) {
        Object obj;
        Object[] args = point.getArgs();
        TUser appUser = (TUser) args[0];
        try {
           obj = point.proceed();
           if (ObjectUtil.isAllNotEmpty(obj, appUser)) {
               R result = (R) obj;
               if (ObjectUtil.isNotNull(result.getData())) {
                   Map fieldMap = this.obj2Map(result);
                   Integer appId = (Integer) fieldMap.get(SAVE_READ_FIELD_APPID);
                   if (ObjectUtil.isNull(appId)) {
                       appId = (Integer) fieldMap.get(SAVE_READ_FIELD_USERID);
                   }
                   if (ObjectUtil.isAllNotEmpty(appUser.getId(), appId)) {
                       if (!appUser.getId().equals(appId)) {
                           log.info("unsafe read occurred, current app id is:{}, result app id is:{}", appUser.getId(), appId);
                           return R.ok();
                       }
                   }
               }
           }
        } catch (Exception e) {
            throw e;
        }
        return obj;
    }

    public Map<String, Object> obj2Map(R result) throws Exception{
        Object record;
        Object obj = result.getData();
        if (obj instanceof List) {
            record = ((List) obj).get(0);
        } else {
            record = obj;
        }
        Field[] fields = record.getClass().getDeclaredFields();
        Map map = new HashMap<String, Object>(fields.length);
        for(Field field:fields){
            field.setAccessible(true);
            map.put(field.getName(), field.get(record));
        }
        return map;
    }
}
