package com.platform.charger.app.config;

import com.platform.charger.app.constants.ChargerAppConstants;
import com.platform.charger.app.service.ChargerMessageService;
import com.platform.charger.business.api.constant.ChargerProtocolConstant;
import com.platform.platform.admin.api.constant.PlatformConstant;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.*;
import org.springframework.amqp.rabbit.connection.CachingConnectionFactory;
import org.springframework.amqp.rabbit.connection.ConnectionFactory;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.amqp.rabbit.listener.SimpleMessageListenerContainer;
import org.springframework.amqp.rabbit.listener.api.ChannelAwareMessageListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

/**
 * @Created with IDEA
 * @author:sunbaolin
 * @Date:2019/6/26
 * @Time:15:22
 */
@Slf4j
@Configuration
@Component
public class ChargerRabbitMQConfig {
	@Value("${spring.rabbitmq.host}")
	private String addresses;

	@Value("${spring.rabbitmq.port}")
	private String port;

	@Value("${spring.rabbitmq.username}")
	private String username;

	@Value("${spring.rabbitmq.password}")
	private String password;

	@Value("${spring.rabbitmq.virtual-host}")
	private String virtualHost;

	@Value("${spring.rabbitmq.publisher-confirms}")
	private boolean publisherConfirms;

	@Autowired
	private ChargerMessageService chargerMessageService;

	@Bean
	public ConnectionFactory connectionFactory() {

		CachingConnectionFactory connectionFactory = new CachingConnectionFactory();
		connectionFactory.setAddresses(addresses + ":" + port);
		connectionFactory.setUsername(username);
		connectionFactory.setPassword(password);
		connectionFactory.setVirtualHost(virtualHost);
		/** 如果要进行消息回调，则这里必须要设置为true */
		connectionFactory.setPublisherConfirmType(CachingConnectionFactory.ConfirmType.CORRELATED);
		connectionFactory.setChannelCacheSize(25);
		return connectionFactory;
	}

	@Bean
	/** 因为要设置回调类，所以应是prototype类型，如果是singleton类型，则回调类为最后一次设置 */
	@Scope(ConfigurableBeanFactory.SCOPE_PROTOTYPE)
	public RabbitTemplate rabbitTemplate() {
		RabbitTemplate template = new RabbitTemplate(connectionFactory());
		template.setMandatory(true);
		template.setUsePublisherConnection(Boolean.TRUE);
		return template;
	}

	@Bean(value = ChargerProtocolConstant.PLATFORM_DELAY)
	CustomExchange customExchange() {
		Map<String, Object> args = new HashMap<>(16);
		args.put("x-delayed-type", "direct");
		return new CustomExchange(ChargerProtocolConstant.PLATFORM_DELAY, "x-delayed-message", true, false, args);
	}

	@Bean(value = ChargerAppConstants.PLATFORM_APP_DELAY_3DS_QUEUE)
	public Queue pay3dsDelayQueue() {
		return new Queue(ChargerAppConstants.PLATFORM_APP_DELAY_3DS_QUEUE, true);
	}

	@Bean
	public Binding bindingPay3dsDelayExchangeMessage(@Qualifier(ChargerAppConstants.PLATFORM_APP_DELAY_3DS_QUEUE) Queue queue, @Qualifier(ChargerProtocolConstant.PLATFORM_DELAY) CustomExchange exchange) {
		return BindingBuilder.bind(queue).to(exchange).with(ChargerAppConstants.PLATFORM_APP_DELAY_3DS_KEY).noargs();
	}

	@Bean
	public SimpleMessageListenerContainer startChargeDelayMessageContainer() {
		SimpleMessageListenerContainer container = new SimpleMessageListenerContainer(connectionFactory());
		container.setQueues(pay3dsDelayQueue());
		container.setExposeListenerChannel(true);
		container.setMaxConcurrentConsumers(50);
		container.setConcurrentConsumers(20);
		container.setAcknowledgeMode(AcknowledgeMode.NONE);
		container.setMessageListener((ChannelAwareMessageListener) (message, channel) -> {
			try {
				log.info("3ds delay task:{}", new String(message.getBody()));
				chargerMessageService.pay3ds(message);
			} catch (Exception e) {
				log.info("{}", e);
			}
		});
		return container;
	}

	/**
	 * 消息交换机
	 */
	@Bean(value = ChargerProtocolConstant.PLATFORM_PUBLIC)
	TopicExchange platformExchange() {
		return new TopicExchange(ChargerProtocolConstant.PLATFORM_PUBLIC);
	}

	/**
	 * 支付通道变更消息队列
	 */
	@Bean(value = PlatformConstant.PLATFORM_PAY_CHANNEL_CHANGE_QUEUE)
	public Queue payChannelQueue() {
		//队列消息持久化
		return new Queue(PlatformConstant.PLATFORM_PAY_CHANNEL_CHANGE_QUEUE, true, false, true);
	}

	/**
	 * 支付通道变更消息路由
	 */
	@Bean
	public Binding bindingPayChannelExchangeMessage(@Qualifier(PlatformConstant.PLATFORM_PAY_CHANNEL_CHANGE_QUEUE) Queue queue, @Qualifier(ChargerProtocolConstant.PLATFORM_PUBLIC) TopicExchange exchange) {
		return BindingBuilder.bind(queue).to(exchange).with(PlatformConstant.PLATFORM_PAY_CHANNEL_CHANGE_KEY);
	}

	@Bean
	public SimpleMessageListenerContainer protocolChargerMessageContainer() {
		SimpleMessageListenerContainer container = new SimpleMessageListenerContainer(connectionFactory());
		container.setQueues(payChannelQueue());
		container.setExposeListenerChannel(true);
		container.setMaxConcurrentConsumers(5);
		container.setConcurrentConsumers(1);
		container.setAcknowledgeMode(AcknowledgeMode.NONE);
		container.setMessageListener((ChannelAwareMessageListener) (message, channel) -> {
			try {
				log.info("pay channel change:{},{}", message.getMessageProperties(), new String(message.getBody()));
				chargerMessageService.payChannel(message);
			} catch (Exception e) {
				log.info("{}", e);
			}
		});
		return container;
	}

	/**
	 * 充电订单消息队列
	 */
	@Bean(value = ChargerProtocolConstant.PLATFORM_BUSINESS_PUSH_ORDER_QUEUE)
	public Queue chargerOrderQueue() {
		//队列消息持久化
		return new Queue(ChargerProtocolConstant.PLATFORM_BUSINESS_PUSH_ORDER_QUEUE, true, false, false);
	}

	/**
	 * 充电订单消息路由
	 */
	@Bean
	public Binding bindingChargerOrderExchangeMessage(@Qualifier(ChargerProtocolConstant.PLATFORM_BUSINESS_PUSH_ORDER_QUEUE) Queue queue, @Qualifier(ChargerProtocolConstant.PLATFORM_PUBLIC) TopicExchange exchange) {
		return BindingBuilder.bind(queue).to(exchange).with(ChargerProtocolConstant.PLATFORM_BUSINESS_PUSH_ORDER_KEY);
	}

	@Bean
	public SimpleMessageListenerContainer protocolChargerOrderMessageContainer() {
		SimpleMessageListenerContainer container = new SimpleMessageListenerContainer(connectionFactory());
		container.setQueues(chargerOrderQueue());
		container.setExposeListenerChannel(true);
		container.setMaxConcurrentConsumers(50);
		container.setConcurrentConsumers(10);
		container.setAcknowledgeMode(AcknowledgeMode.NONE);
		container.setMessageListener((ChannelAwareMessageListener) (message, channel) -> {
			try {
				log.info("charging order info:{},{}", message.getMessageProperties(), new String(message.getBody()));
				chargerMessageService.chargerOrder(message);
			} catch (Exception e) {
				log.info("{}", e);
			}
		});
		return container;
	}

	/**
	 * 充电订单消息队列
	 */
	@Bean(value = ChargerProtocolConstant.PLATFORM_BUSINESS_PUSH_RESERVE_ORDER_QUEUE)
	public Queue reserveOrderQueue() {
		//队列消息持久化
		return new Queue(ChargerProtocolConstant.PLATFORM_BUSINESS_PUSH_RESERVE_ORDER_QUEUE, true, false, false);
	}

	/**
	 * 充电订单消息路由
	 */
	@Bean
	public Binding bindingReserveOrderExchangeMessage(@Qualifier(ChargerProtocolConstant.PLATFORM_BUSINESS_PUSH_RESERVE_ORDER_QUEUE) Queue queue, @Qualifier(ChargerProtocolConstant.PLATFORM_PUBLIC) TopicExchange exchange) {
		return BindingBuilder.bind(queue).to(exchange).with(ChargerProtocolConstant.PLATFORM_BUSINESS_PUSH_RESERVE_ORDER_KEY);
	}

	@Bean
	public SimpleMessageListenerContainer protocolReserveOrderMessageContainer() {
		SimpleMessageListenerContainer container = new SimpleMessageListenerContainer(connectionFactory());
		container.setQueues(reserveOrderQueue());
		container.setExposeListenerChannel(true);
		container.setMaxConcurrentConsumers(50);
		container.setConcurrentConsumers(10);
		container.setAcknowledgeMode(AcknowledgeMode.NONE);
		container.setMessageListener((ChannelAwareMessageListener) (message, channel) -> {
			try {
				log.info("reserve order info:{},{}", message.getMessageProperties(), new String(message.getBody()));
				chargerMessageService.reserveOrder(message);
			} catch (Exception e) {
				log.info("{}", e);
			}
		});
		return container;
	}
}
