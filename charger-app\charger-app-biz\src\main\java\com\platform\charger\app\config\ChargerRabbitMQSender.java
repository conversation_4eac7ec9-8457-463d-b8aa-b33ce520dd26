package com.platform.charger.app.config;

import cn.hutool.core.util.IdUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.AmqpException;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.core.MessagePostProcessor;
import org.springframework.amqp.rabbit.connection.CorrelationData;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @Created with IDEA
 * @author:sunbaolin
 * @Date:2019/6/12
 * @Time:14:09
 */
@Component
@Slf4j
public class ChargerRabbitMQSender implements RabbitTemplate.ConfirmCallback, RabbitTemplate.ReturnCallback {
	@Autowired
	private RabbitTemplate rabbitTemplate;

	public void send(String exchange, String route, byte[] message) {
		rabbitTemplate.setConfirmCallback(this);
		CorrelationData correlationData = new CorrelationData(IdUtil.fastSimpleUUID());

		/*log.info("消息id:{}",correlationData.getId());*/
		//用RabbitMQ发送MQTT需将exchange配置为amq.topic
		this.rabbitTemplate.convertAndSend(exchange, route, message, correlationData);
	}

	public void sendDelay(String exchange, String route, byte[] message, Integer time) {
		rabbitTemplate.setConfirmCallback(this);
		CorrelationData correlationData = new CorrelationData(IdUtil.fastSimpleUUID());
		MessagePostProcessor messagePostProcessor = new MessagePostProcessor() {
			@Override
			public Message postProcessMessage(Message message) throws AmqpException {
				message.getMessageProperties().setDelay(time);
				return message;
			}
		};
		/*log.info("消息id:{}",correlationData.getId());*/
		//用RabbitMQ发送MQTT需将exchange配置为amq.topic
		this.rabbitTemplate.convertAndSend(exchange, route, message, messagePostProcessor, correlationData);
	}

	@Override
	public void confirm(CorrelationData correlationData, boolean ack, String cause) {
		if (ack) {
			/*log.info("消息发送确认成功");*/
		} else {
			log.info("send message error:{}", cause);
		}
	}

	@Override
	public void returnedMessage(Message message, int replyCode, String replyText, String exchange, String routingKey) {
		log.info("return--message:{},replyCode:{},replyText:{},exchange:{},routingKey:{}", new String(message.getBody()), replyCode, replyText, exchange, routingKey);
	}
}
