package com.platform.charger.app.config;

import com.platform.charger.app.interceptor.AuthorizationInterceptor;
import com.platform.charger.app.interceptor.AuthorizationOpsInterceptor;
import com.platform.charger.app.interceptor.ChargerAppInterceptor;
import com.platform.charger.app.resolver.LoginUserHandlerMethodArgumentResolver;
import com.platform.charger.app.resolver.OpsLoginUserHandlerMethodArgumentResolver;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.method.support.HandlerMethodArgumentResolver;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

import java.util.List;

@Configuration
public class WebMvcConfig implements WebMvcConfigurer {
    @Autowired
    private AuthorizationInterceptor authorizationInterceptor;
    @Autowired
    private AuthorizationOpsInterceptor authorizationOpsInterceptor;

    @Autowired
    private ChargerAppInterceptor chargerAppInterceptor;

    @Autowired
    private LoginUserHandlerMethodArgumentResolver loginUserHandlerMethodArgumentResolver;
    @Autowired
    private OpsLoginUserHandlerMethodArgumentResolver opsLoginUserHandlerMethodArgumentResolver;

    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        registry.addInterceptor(authorizationInterceptor).addPathPatterns("/app/**");
        registry.addInterceptor(authorizationOpsInterceptor).addPathPatterns("/ops/**");
        registry.addInterceptor(chargerAppInterceptor).addPathPatterns("/app/**","/web/**");
    }

    @Override
    public void addArgumentResolvers(List<HandlerMethodArgumentResolver> resolvers) {
        resolvers.add(loginUserHandlerMethodArgumentResolver);
        resolvers.add(opsLoginUserHandlerMethodArgumentResolver);
    }
}
