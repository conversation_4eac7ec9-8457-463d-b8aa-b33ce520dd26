package com.platform.charger.app.constants;

import java.math.BigDecimal;

public interface ChargerAppConstants {
	/**
	 *APP户用桩、私人卡每日绑定数量限制
	 */
	String SYS_APP_PILE_CARD_MAX_COUNT = "SYS_APP_PILE_CARD_MAX_COUNT";
	/**
	 *响应APP非车队账户类型
	 */
	Integer APP_NOT_FLEETS_TYPE = 0;
	/**
	 *响应APP车队账户类型
	 */
	Integer APP_FLEETS_TYPE = 1;
	/**
	 *APP车队用户可以使用车队余额充电
	 */
	Integer APP_FLEETS_RESP_ACCEPTED = 0;
	/**
	 *订单类型-APP充电
	 */
	int APP_ORDER_TYPE_APP_CHARGER = 0;
	/**
	 *订单类型-刷卡充电
	 */
	int APP_ORDER_TYPE_CARD_CHARGER = 1;
	/**
	 *订单类型-预约
	 */
	int APP_ORDER_TYPE_RESERVE = 2;
	/**
	 *订单类型-充值
	 */
	int APP_ORDER_TYPE_RECHARGE = 3;
	/**
	 *订单类型-退款
	 */
	int APP_ORDER_TYPE_REFUND = 4;
	/**
	 *订单类型-未支付订单付费
	 */
	int APP_ORDER_TYPE_UNPAID = 5;
	/**
	 *冻结金额结果-成功
	 */
	int APP_PAY_STATUS_CONFIRM_SUCCESS = 0;
	/**
	 *冻结金额结果-失败
	 */
	int APP_PAY_STATUS_FAILED = 1;
	/**
	 *冻结金额结果-失败-需3DS鉴权
	 */
	int APP_PAY_STATUS_NEED_3DS = 2;
	/**
	 *扣款成功
	 */
	int APP_PAY_STATUS_CAPTURE_SUCCESS = 3;

	/**
	 *google推送组件初始化锁
	 */
	String APP_GOOGLE_FIREBASE_LOCK = "APP_GOOGLE_FIREBASE_LOCK_";
    /**
     *用户注册锁
     */
    String APP_USER_REG_REDIS_LOCK = "APP_USER_REG_REDIS_LOCK_";

    /**
     *充电枪状态
     */
    String REDIS_BUS_PILE_GUN_STATUS = "CHARGER_BUS_PILE_GUN_STATUS:";

    /**
     *充电订单实时信息
     */
    String REDIS_BUS_ORDER_METER_VALUE_BY_ID = "REDIS_BUS_ORDER_METER_VALUE_BY_ID:";

    /**
     * 预约订单实时信息
     */
    String REDIS_BUS_RESERVE_ORDER_STATUS_BY_ID = "REDIS_BUS_RESERVE_ORDER_STATUS_BY_ID:";

    /**
     *枪计费规则为一口价
     */
    Integer CHARGER_FEE_TYPE_AMOUNT = 3;

    /**
     *按金额充电
     */
    Integer CHARGER_TYPE_MONEY = 3;
	/**
	 *3DS认证结果锁
	 */
	String REDIS_LOCK_APP_STRIPE_SECURE = "REDIS_LOCK_APP_STRIPE_SECURE_";
    /**
     *充电启动锁
	 *充值\退款锁
     */
    String REDIS_LOCK_APP_START_CHARGE = "REDIS_LOCK_APP_START_CHARGE_";
    /**
     *充电停止锁
     */
    String REDIS_LOCK_APP_STOP_CHARGE = "REDIS_LOCK_APP_STOP_CHARGE_";
    /**
     *预约锁
     */
    String REDIS_LOCK_APP_START_RESERVE = "REDIS_LOCK_APP_START_RESERVE_";

    /**
     *取消预约锁
     */
    String REDIS_LOCK_APP_CANCEL_RESERVE = "REDIS_LOCK_APP_CANCEL_RESERVE_";

	/**
	 *充电记录导出
	 */
	String REDIS_LOCK_APP_CHARGER_ORDER_EXPORT = "REDIS_LOCK_APP_CHARGER_ORDER_EXPORT_";
    /**
     *运营订单
     */
    Integer CHARGER_ORDER_OPERATIVE = 0;

    /**
     *非运营家桩订单
     */
    Integer CHARGER_ORDER_INOPERATIVE = 2;

    /**
     *订单金额异常
     */
    Integer CHARGER_ORDER_PAY_TYPE_ERROR = 2;

    /**
     * 用户shortId生成常量
     */
    String USER_SHORT_ID_CONSTANT = "EVCCE-M000000000";

    /**
     *充电桩在线
     */
    Integer CHARGER_PILE_ONLINE = 1;

    /**
     * 邮箱验证码有效时间/分钟
     */
    Integer EMAIL_CODE_EXPIRE_TIME = 10;

    /**
     * 同一邮箱日发送验证码上限
     */
    Integer APP_VERIFY_CODE_DAY_COUNT_EMAIL = 5;

    /**
     * 同一IP日发送验证码上限
     */
    Integer APP_VERIFY_CODE_DAY_COUNT_IP = 20;

    /**
     * 充电订单信息
     */
    String REDIS_BUS_ORDER_INFO_BY_ID = "REDIS_BUS_ORDER_INFO_BY_ID:";

    /**
     * 充电订单缓存周期
     */
    Long REDIS_BUS_ORDER_SAVE_CYCLE = 1L;

    /**
     * 预约订单信息
     */
    String REDIS_BUS_RESERVE_ORDER_BY_ID = "REDIS_BUS_RESERVE_ORDER_VALUE_BY_ID:";

    /**
     *卡状态 0正常
     */
    int CHARGER_BUS_CARD_STATUS_NORMAL = 0;

    /**
     * 卡状态 1挂失
     */
    int CHARGER_BUS_CARD_STATUS_LOSS = 1;

	/**
	 * 3DS认证等待延迟路由
	 */
	String PLATFORM_APP_DELAY_3DS_KEY = "platform.delay.3ds.key";
	/**
	 * 3DS认证等待延迟队列
	 */
	String PLATFORM_APP_DELAY_3DS_QUEUE = "platform.delay.3ds.queue";
	/**
	 * 3DS认证等待延迟时长,6min
	 */
	Integer PLATFORM_APP_DELAY_3DS_TIME = 360000;
	/**
	 * 3DS认证等待重试延迟时长,30sec
	 */
	Integer PLATFORM_APP_DELAY_3DS_RETRY_TIME = 30000;

	/**
	 * 启动充电默认预扣款金额
	 */
	BigDecimal PLATFORM_APP_WITH_HOLDING_AMOUNT = BigDecimal.valueOf(99);
}
