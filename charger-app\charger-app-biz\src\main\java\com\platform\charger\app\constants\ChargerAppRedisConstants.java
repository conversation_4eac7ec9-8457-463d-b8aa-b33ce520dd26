package com.platform.charger.app.constants;

public interface ChargerAppRedisConstants {
	/**
	 * App用户信息缓存
	 */
	String APP_USER_EMAIL_INFO = "APP_USER_EMAIL_INFO:";
    /**
     * App用户信息缓存
     */
    String APP_USER_INFO = "APP_USER_INFO:";

    /**
     * 运维用户信息缓存
     */
    String OPS_USER_INFO = "OPS_USER_INFO:";

    /**
     * 用户过去注册数量
     */
    String APP_USER_BEFORE_REGISTER_COUNT = "APP_USER_BEFORE_REGISTER_COUNT:";

    /**
     * 用户当天注册数量
     */
    String APP_USER_CURRENT_REGISTER_COUNT = "APP_USER_CURRENT_REGISTER_COUNT:";

    /**
     * 用户每天活跃数
     */
    String APP_USER_ACTIVE_COUNT = "APP_USER_ACTIVE_COUNT:";

    /**
     * 家桩绑定用户ID集合
     */
    String HOME_PILE_BIND_USERID_LIST = "HOME_PILE_BIND_USERID_LIST";

    /**
     * 重置密码的邮箱验证码<前缀+邮箱，验证码>
     */
    String RESET_PASSWORD_EMAIL_CODE = "RESET_PASSWORD_EMAIL_CODE:";

    /**
     * 用户注册的邮箱验证码<前缀+邮箱，验证码>
     */
    String REG_EMAIL_CODE = "REG_EMAIL_CODE:";

    /**
     * 重置密码邮箱验证码锁
     */
    String REDIS_LOCK_RESET_PASSWORD = "REDIS_LOCK_RESET_PASSWORD_";

    /**
     * 用户注册邮箱验证码锁
     */
    String REDIS_LOCK_USER_REG = "REDIS_LOCK_USER_REG_";

    /**
     * 邮箱验证码日发送数量
     */
    String REDIS_EMAIL_CODE_DAY_COUNT = "REDIS_EMAIL_CODE_DAY_COUNT:";

    /**
     * 修改邮箱的邮箱验证码<前缀+邮箱，验证码>
     */
    String UPDATE_EMAIL_CODE = "UPDATE_EMAIL_CODE:";

    /**
     * 用户修改邮箱验证码锁
     */
    String REDIS_LOCK_UPDATE_EMAIL = "REDIS_LOCK_UPDATE_EMAIL_";

    /**
     * 预约充电设置缓存结果，供APP查询
     * <key+appId,pileId，结果>
     */
    String REDIS_PERSONAL_PILE_RESERVE_RESULT = "REDIS_PERSONAL_PILE_RESERVE_RESULT:";

    /**
     *家桩预约规则设置锁
     */
    String REDIS_LOCK_APP_HOME_PILE_RESERVE_SETTING = "REDIS_LOCK_APP_HOME_PILE_RESERVE_SETTING:";

    /**
     * 动态功率设置缓存结果，供APP查询
     * <key+指令id，结果>
     */
    String REDIS_DYNAMIC_POWER_CTRL_RESULT = "REDIS_DYNAMIC_POWER_CTRL_RESULT:";

    /**
     * 家桩持有人用户ID缓存
     * <key,pileId,userId>
     */
    String REDIS_HOME_PILE_OWNER_ID = "REDIS_HOME_PILE_OWNER_ID:";

    /**
     * 充电实时信息
     */
    String REDIS_BUS_METER_VALUE_BY_PILE_ID = "REDIS_BUS_METER_VALUE_BY_PILE_ID:";

    /**
     * App数字统计结果缓存
     */
    String REDIS_APP_DIGITAL_STATISTICS = "REDIS_APP_DIGITAL_STATISTICS:";

    /**
     * App月柱形统计结果缓存
     */
    String REDIS_APP_MONTH_COLUMN_STATISTICS = "REDIS_APP_MONTH_COLUMN_STATISTICS:";

    /**
     * App周柱形统计结果缓存
     */
    String REDIS_APP_WEEK_COLUMN_STATISTICS = "REDIS_APP_WEEK_COLUMN_STATISTICS:";

    /**
     * App饼图统计结果缓存
     */
    String REDIS_APP_PIE_STATISTICS = "REDIS_APP_PIE_STATISTICS:";

    /**
     * APP用户token缓存
     */
    String REDIS_APP_USER_TOKEN = "REDIS_APP_USER_TOKEN:";

	/**
	 * APP用户每日新增户用桩阈值缓存
	 */
    String REDIS_APP_PILE_INCR = "REDIS_APP_PILE_INCR:";

	/**
	 * APP用户每日新增户用私人卡阈值缓存
	 */
	String REDIS_APP_CARD_INCR = "REDIS_APP_CARD_INCR:";
}
