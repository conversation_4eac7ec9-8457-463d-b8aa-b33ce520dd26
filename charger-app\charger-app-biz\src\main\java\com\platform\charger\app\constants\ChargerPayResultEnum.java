package com.platform.charger.app.constants;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @Created with IDEA
 * @author:sunbaolin
 * @Date:2022/6/21
 * @Time:15:50
 */
@Getter
@AllArgsConstructor
public enum ChargerPayResultEnum {
	CUSTOMER_CREATE_FAILED(1,"stripe customer create failed"),
	CREDIT_INFO_NOT_EXIST(2,"credit card info not exist"),
	CREDIT_USER_NOT_MATCH(3,"credit card user not match"),
	PAYMENT_INTENT_CREATE_FAILED(4,"create paymentIntent failed"),
	CREDIT_NEED_3DS_AUTH(5,"credit card need 3ds auth"),
	CREDIT_PAY_FAILED(6,"credit card pay failed"),
	CREDIT_CONFIRM_SUCCESS(7,"credit card confirm success"),
	CREDIT_CAPTURE_SUCCESS(8,"credit card capture success");
	private Integer key;
	private String value;
}
