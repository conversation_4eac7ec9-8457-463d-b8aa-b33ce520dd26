package com.platform.charger.app.constants;

/**
 * @Created with IDEA
 * @author:sunbaolin
 * @Date:2022/6/21
 * @Time:17:49
 */
public interface ChargerStripePaymentStatusEnum {
	String REQUIRES_PAYMENT_METHOD = "requires_payment_method";
	String REQUIRES_CONFIRMATION = "requires_confirmation";
	String REQUIRES_ACTION = "requires_action";
	String PROCESSING = "processing";
	String REQUIRES_CAPTURE = "requires_capture";
	String CANCELED = "canceled";
	String SUCCEEDED = "succeeded";
	String PENDING = "pending";
	String FAILED = "failed";
}
