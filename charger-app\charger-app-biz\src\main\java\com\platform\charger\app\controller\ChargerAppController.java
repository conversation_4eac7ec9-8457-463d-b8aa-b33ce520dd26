package com.platform.charger.app.controller;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.servlet.ServletUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.platform.charger.app.annotation.ChargerApp;
import com.platform.charger.app.annotation.SafeRead;
import com.platform.charger.app.api.entity.ChargerUserFeedbackEntity;
import com.platform.charger.app.api.entity.TUser;
import com.platform.charger.app.api.entity.TUserCard;
import com.platform.charger.app.api.entity.TUserVehicle;
import com.platform.charger.app.constants.ChargerAppConstants;
import com.platform.charger.app.constants.ChargerAppRedisConstants;
import com.platform.charger.app.entity.*;
import com.platform.charger.app.service.*;
import com.platform.charger.business.api.entity.*;
import com.platform.charger.business.api.feign.RemoteBusinessService;
import com.platform.platform.common.core.constant.SecurityConstants;
import com.platform.platform.common.core.constant.enums.ResponseTypeEnum;
import com.platform.platform.common.core.util.R;
import com.platform.platform.common.lock.util.RedissLockUtil;
import com.platform.platform.common.security.annotation.Login;
import com.platform.platform.common.security.annotation.LoginUser;
import com.platform.platform.file.api.entity.QueryChargerAppOrderEntity;
import com.platform.platform.file.api.feign.RemoteFileUploadService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;

@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/app")
public class ChargerAppController {
    private final ChargerAppOrderService chargerAppOrderService;
    private final RemoteBusinessService remoteBusinessService;
    private final ChargerAppUserService chargerAppUserService;
    private final ChargerAppCollectService chargerAppCollectService;
    private final ChargerAppVehicleService chargerAppVehicleService;
    private final ChargerAppDeviceService chargerAppDeviceService;
    private final ChargerAppOrderStatisticsService chargerAppOrderStatisticsService;
    private final ChargerAppEnergyBoxService chargerAppEnergyBoxService;
    private final ChargerAppUserCardService chargerAppUserCardService;
    private final ChargerRedisService chargerRedisService;
    private final ChargerAppVersionService chargerAppVersionService;
    private final ChargerAppCreditCardService chargerAppCreditCardService;
    private final ChargerUserFeedbackService chargerUserFeedbackService;
	private final RemoteFileUploadService remoteFileUploadService;

    /**----------------------用户操作相关接口------------------------------------*/
    /**
     *注册
     */
    @PostMapping("/user/reg")
    public R reg(@RequestHeader("uuid") String payUuid, @Valid @RequestBody ChargerAppRegDto entity, HttpServletRequest request) {
        try {
            entity.setPayUuid(payUuid);
            entity.setSRegSource(request.getHeader("user-agent"));
            entity.setLastLoginTime(DateUtil.now());
            entity.setLastIp(ServletUtil.getClientIP(request));
            return chargerAppUserService.appReg(entity);
        } catch (Exception e) {
            log.info("user email:{} register error，time:{},error:{}", entity.getEmail(), DateUtil.now(), e);
            return R.ok(ResponseTypeEnum.GLOBAL_REQUEST_FAILED);
        }
    }

    /**
     *登录
     */
    @PostMapping("/user/pwdLogin")
    public R pwdLogin(@RequestHeader("uuid") String payUuid, @Valid @RequestBody ChargerAppLoginDto entity, HttpServletRequest request) {
        entity.setPayUuid(payUuid);
        entity.setSRegSource(request.getHeader("user-agent"));
        entity.setLastLoginTime(DateUtil.now());
        entity.setLastIp(ServletUtil.getClientIP(request));
        return chargerAppUserService.pwdLogin(entity);
    }

    /**
     * 用户注册或登录后，异步更新用户的devToken
     */
    @Login
    @PostMapping("/user/setDevToken")
    public R setDevToken(@LoginUser TUser user, @RequestParam("devToken") String devToken) {
        return chargerAppUserService.setDevToken(user.getId(), devToken);
    }

    /**
     *修改密码
     */
    @Login
    @PostMapping("/user/updatePwd")
    public R updatePwd(@Valid @RequestBody UpdatePwdDto dto, @LoginUser TUser user) {
        return chargerAppUserService.updatePwd(dto, user);
    }

    /**
     * 修改用户个人信息
     */
    @Login
    @PostMapping("/user/updateUserInfo")
    public R updateUserInfo(@Valid @RequestBody UpdateUserInfoDto dto, @LoginUser TUser user) {
        return chargerAppUserService.updateUserInfo(dto, user.getId());
    }

    /**
     *刷新token
     */
    @PostMapping("/user/refreshToken")
    public R refreshToken(@RequestHeader("token") String token) {
        return chargerAppUserService.refreshToken(token);
    }

    /**
     *获取个人信息
     */
    @Login
    @PostMapping("/user/getUserInfo")
    public R getUserInfo(@LoginUser TUser loginUser) {
        return chargerAppUserService.getUserInfo(loginUser.getId());
    }

    /**
     *注销
     */
    @Login
    @PostMapping("/user/logout")
    public R logout(@LoginUser TUser loginUser) {
        return chargerAppUserService.logout(loginUser);
    }

    /**
     *修改邮箱
     */
    @Login
    @PostMapping("/user/updateEmail")
    public R updateEmail(@Valid @RequestBody UpdateEmailDto dto, @LoginUser TUser loginUser) {
        return chargerAppUserService.updateEmail(dto, loginUser);
    }

    /**
     *收藏站点
     */
    @Login
    @PostMapping("/user/collectStation")
    public R collectStation(@RequestParam("id") Integer stationId, @LoginUser TUser loginUser) {
        return chargerAppCollectService.collectStation(stationId, loginUser);
    }

    /**
     *分页获取收藏站点列表
     */
    @Login
    @PostMapping("/user/getCollectStationPage")
    public R getCollectStationPage(@LoginUser TUser loginUser) {
        return chargerAppUserService.getCollectStationPage(loginUser.getId());
    }

    /**
     * 修改密码界面发送重置密码邮箱验证码
     */
    @Login
    @PostMapping("/user/sendResetPwdEmailCode")
    public R sendResetPasswordEmailCode(@LoginUser TUser loginUser, HttpServletRequest request) {
        String ip = ServletUtil.getClientIP(request);
        return chargerAppUserService.sendResetPasswordEmailCode(loginUser, ip);
    }

    /**
     * 修改密码界面重置密码
     */
    @Login
    @PostMapping("/user/resetPassword")
    public R resetPassword(@Valid @RequestBody ResetPasswordDto dto, @LoginUser TUser loginUser) {
        return chargerAppUserService.resetPassword(dto, loginUser);
    }

    /**
     *登录界面发送重置密码邮箱验证码
     */
    @PostMapping("/user/sendResetPwdEmailCode1")
    public R sendResetPasswordEmailCode1(@RequestHeader(required = false,value = "uuid") String payUuid,@RequestParam("email") String email, HttpServletRequest request) {
        String ip = ServletUtil.getClientIP(request);
        return chargerAppUserService.sendResetPasswordEmailCode1(email, ip,payUuid);
    }

    /**
     * 登录界面重置密码
     */
    @PostMapping("/user/resetPassword1")
    public R resetPassword1(@RequestHeader(value = "uuid", required = false) String payUuid, @Valid @RequestBody ResetPasswordDto dto) {
        dto.setPayUuid(payUuid);
        return chargerAppUserService.resetPassword1(dto);
    }

    /**
     * 发送用户注册邮箱验证码
     */
    @PostMapping("/user/sendRegEmailCode")
    public R sendRegEmailCode(@RequestHeader(required = false,value = "uuid") String payUuid,@RequestParam("email") String email, HttpServletRequest request) {
        String ip = ServletUtil.getClientIP(request);
        return chargerAppUserService.sendRegEmailCode(email, ip,payUuid);
    }

    /**
     * 发送用户修改邮箱的邮箱验证码
     */
    @Login
    @PostMapping("/user/sendUpdateEmailCode")
    public R sendUpdateEmailCode(@Valid @RequestBody SendUpdateEmailCodeDto dto, @LoginUser TUser loginUser, HttpServletRequest request) {
        String ip = ServletUtil.getClientIP(request);
        dto.setIp(ip);
        return chargerAppUserService.sendUpdateEmailCode(dto, loginUser);
    }

    /**
     * 获取用户订单统计数据
     */
    @Login
    @PostMapping("/user/getAppChargerOrderStatistics")
    public R getAppChargerOrderStatistics(@RequestBody QueryAppOrderStatisticsDto dto, @LoginUser TUser loginUser) {
        dto.setUserId(loginUser.getId());
        return chargerAppOrderStatisticsService.getAppChargerOrderStatistics(dto);
    }

    /**
     * 获取APP版本信息
     */
    @PostMapping("/user/getAppVersion")
    public R getAppVersion(@RequestHeader("uuid") String uuid, @RequestParam("type") Integer type) {
        return chargerAppVersionService.getAppVersion(uuid, type);
    }

    /**
     * 用户提交问题反馈
     */
    @Login
    @PostMapping("/user/commitFeedBackContent")
    public R commitFeedBackContent(@Valid @RequestBody ChargerUserFeedbackEntity entity, @LoginUser TUser loginUser) {
        return chargerUserFeedbackService.commitFeedBackContent(entity, loginUser);
    }

    /**
     * 用户获取问题反馈列表
     */
    @Login
    @PostMapping("/user/getFeedBackContentList")
    public R getFeedBackContentList(@LoginUser TUser loginUser) {
        return chargerUserFeedbackService.getFeedBackContentList(loginUser.getId());
    }

    /**
     * 用户上传问题反馈图片
     */
    @PostMapping("/user/uploadFeedBackImage")
    public R uploadFeedBackImage(@RequestParam("file") MultipartFile files[]) {
        return chargerUserFeedbackService.uploadFeedBackImage(files);
    }

    /**
     * 用户删除问题反馈图片
     */
    @PostMapping("/user/delFeedBackImage")
    public R delFeedBackImage(@RequestParam("imgPath") String imgPath) {
        return chargerUserFeedbackService.delFeedBackImage(imgPath);
    }

	@Login
	@PostMapping("/user/recharge")
	public R recharge(@Valid @RequestBody ChargerAppRechargeEntity entity, @LoginUser TUser loginUser)
	{
		String lockKey = new StringBuilder(ChargerAppConstants.REDIS_LOCK_APP_START_CHARGE).append(loginUser.getId()).toString();
		Boolean lock = Boolean.FALSE;
		try {
			lock = RedissLockUtil.lock(lockKey);
			if (lock) {
				log.info("user:{},email:{} start recharge:{}", loginUser.getId(), loginUser.getEmail(), JSON.toJSONString(entity));
				return chargerAppOrderService.recharge(entity,loginUser);
			}
		} catch (Exception e) {
			log.info("{}", e);
		} finally {
			if (lock) {
				RedissLockUtil.unlock(lockKey);
			}
		}
		return R.ok(ResponseTypeEnum.GLOBAL_REQUEST_FAILED);
	}

	@Login
	@PostMapping("/user/refund")
	public R refund( @LoginUser TUser loginUser)
	{
		String lockKey = new StringBuilder(ChargerAppConstants.REDIS_LOCK_APP_START_CHARGE).append(loginUser.getId()).toString();
		Boolean lock = Boolean.FALSE;
		try {
			lock = RedissLockUtil.lock(lockKey);
			if (lock) {
				log.info("user:{},email:{} start refund", loginUser.getId(),loginUser.getEmail());
				return chargerAppOrderService.refund(loginUser);
			}
		} catch (Exception e) {
			log.info("start refund error:{}", e);
		} finally {
			if (lock) {
				RedissLockUtil.unlock(lockKey);
			}
		}
		return R.ok(ResponseTypeEnum.GLOBAL_REQUEST_FAILED);
	}

    /**----------------------用户订单相关接口------------------------------------*/
    /**
     *扫码
     */
    @Login
    @PostMapping("/order/check")
    public R checkChargeStatus(@RequestBody ChargerScanDto entity, @LoginUser TUser loginUser) {
        try {
            String scanData = entity.getScanData();
            if (StrUtil.isNotBlank(scanData)) {
                log.info("scan data:{}", scanData);
                return chargerAppOrderService.checkScanStatus(scanData, loginUser.getId());
            }
            return R.ok(ResponseTypeEnum.GLOBAL_REQUEST_FAILED);
        } catch (Exception e) {
            log.info("scan error:{}", e);
            return R.ok(ResponseTypeEnum.GLOBAL_REQUEST_FAILED);
        }
    }

    /**
     *启动充电
     */
    @Login
    @PostMapping("/order/startCharge")
    public R startCharge(@Valid @RequestBody ChargerOrderDto orderDto, @LoginUser TUser loginUser) {
        String lockKey = new StringBuilder(ChargerAppConstants.REDIS_LOCK_APP_START_CHARGE).append(loginUser.getId()).toString();
        Boolean lock = Boolean.FALSE;
        try {
            lock = RedissLockUtil.lock(lockKey);
            if (lock) {
                log.info("user:{} start charge:{},time:{}", loginUser.getId(), JSON.toJSONString(orderDto),DateUtil.now());
                R result = chargerAppOrderService.startChargerOrder(orderDto, loginUser);
                //3ds验证需要返回一个key值
                return result;
            }
        } catch (Exception e) {
            log.info("start charge error:{}", e);
        } finally {
            if (lock) {
                RedissLockUtil.unlock(lockKey);
            }
        }
        return R.ok(ResponseTypeEnum.GLOBAL_REQUEST_FAILED);
    }

    @Login
	@PostMapping("/order/secure")
	public R order3DS(@Valid @RequestBody ChargerStripeSecureEntity entity,@LoginUser TUser loginUser)
	{
		String lockKey = new StringBuilder(ChargerAppConstants.REDIS_LOCK_APP_STRIPE_SECURE).append(entity.getStripeId()).toString();
		Boolean lock = Boolean.FALSE;
		try {
			lock = RedissLockUtil.lock(lockKey);
			if (lock) {
				log.info("3DS:{},user:{}", entity.getStripeId(),loginUser.getId());
				R result = chargerAppOrderService.stripeSecure(entity, loginUser);
				return result;
			}
		} catch (Exception e) {
			log.info("3ds error:{}", e);
		} finally {
			if (lock) {
				RedissLockUtil.unlock(lockKey);
			}
		}
		return R.ok(ResponseTypeEnum.GLOBAL_REQUEST_FAILED);
	}

	@Login
	@PostMapping("/order/pay")
	public R orderPay(@Valid @RequestBody ChargerUnpaidOrderEntity entity,@LoginUser TUser loginUser)
	{
		String lockKey = new StringBuilder(ChargerAppConstants.REDIS_LOCK_APP_START_CHARGE).append(loginUser.getId()).toString();
		Boolean lock = Boolean.FALSE;
		try {
			lock = RedissLockUtil.lock(lockKey);
			if (lock) {
				log.info("order pay:{},user:{}", JSON.toJSONString(entity),loginUser.getId());
				R result = chargerAppOrderService.unPaidOrder(entity, loginUser);
				return result;
			}
		} catch (Exception e) {
			log.info("order pay error:{}", e);
		} finally {
			if (lock) {
				RedissLockUtil.unlock(lockKey);
			}
		}
		return R.ok(ResponseTypeEnum.GLOBAL_REQUEST_FAILED);
	}

    /**
     * 获取充电订单实时数据
     */
    @SafeRead
    @Login
    @PostMapping("/order/getRealOrderInfo")
    public R getRealOrderInfo(@LoginUser TUser loginUser, @RequestParam("id") Integer orderId) {
        return chargerAppOrderService.getRealOrderInfo(orderId);
    }

    /**
     * 根据桩枪获取充电订单实时数据
     */
    @Login
    @PostMapping("/order/getRealOrderInfoByPile")
    public R getRealOrderInfoByPile(@RequestParam("pileId") Integer pileId, @RequestParam("gunNo") Integer gunNo, @LoginUser TUser loginUser) {
        QueryChargerOrderEntity queryEntity = new QueryChargerOrderEntity();
        queryEntity.setPileId(pileId);
        queryEntity.setGunNo(gunNo);
        queryEntity.setAppId(loginUser.getId());
        return chargerAppOrderService.getRealOrderInfoByPile(queryEntity);
    }

    /**
     *停止充电
     */
    @Login
    @PostMapping("/order/stopCharge")
    public R stopCharge(@RequestParam("id") Integer orderId, @LoginUser TUser loginUser) {
        String lockKey = new StringBuilder(ChargerAppConstants.REDIS_LOCK_APP_STOP_CHARGE).append(orderId).toString();
        Boolean lock = Boolean.FALSE;
        try {
            lock = RedissLockUtil.lock(lockKey);
            if (lock) {
                log.info("user:{} stop charge,time:{}", loginUser.getId(), DateUtil.now());
                R result = chargerAppOrderService.stopChargeOrder(orderId);
                return result;
            }
        } catch (Exception e) {
            log.info("stop charge error:{}", e);
        } finally {
            if (lock) {
                RedissLockUtil.unlock(lockKey);
            }
        }
        return R.ok(ResponseTypeEnum.GLOBAL_REQUEST_FAILED);
    }

    /**
     *获取充电订单详情
     */
    @SafeRead
    @Login
    @PostMapping("/order/getChargeOrderInfo")
    public R getChargeOrderInfo(@LoginUser TUser loginUser, @RequestParam(value = "id") Integer orderId) {
        try {
            R<ChargerOrderEntity> orderResult = remoteBusinessService.getChargeOrderInfo(orderId, SecurityConstants.FROM_IN);
            if (ObjectUtil.isNotNull(orderResult) && 0 == orderResult.getCode() && ObjectUtil.isNotNull(orderResult.getData())) {
                ChargerAppOrderEntity appOrderEntity = new ChargerAppOrderEntity();
                BeanUtils.copyProperties(orderResult.getData(), appOrderEntity);
                if (ChargerAppConstants.CHARGER_ORDER_OPERATIVE.equals(appOrderEntity.getOrderFlag())) {
                    R<ChargerStationEntity> stationResult = remoteBusinessService.getStationInfoById(appOrderEntity.getStationId(), SecurityConstants.FROM_IN);
                    if (ObjectUtil.isNotNull(stationResult) && 0 == stationResult.getCode() && ObjectUtil.isNotNull(stationResult.getData())) {
                        appOrderEntity.setAddress(stationResult.getData().getAddress());
                    }
                }
                return R.ok(appOrderEntity);
            }
            return R.ok(ResponseTypeEnum.GLOBAL_REQUEST_FAILED);
        } catch (Exception e) {
            log.info("get charging order info error:{}", e);
            return R.ok(ResponseTypeEnum.GLOBAL_REQUEST_FAILED);
        }
    }

    /**
     *启动预约
     */
    @Login
    @PostMapping("/order/startReserve")
    public R startReserve(@Valid @RequestBody ReserveOrderDto orderDto, @LoginUser TUser loginUser) {
        String lockKey = new StringBuilder(ChargerAppConstants.REDIS_LOCK_APP_START_RESERVE).append(loginUser.getId()).toString();
        Boolean lock = Boolean.FALSE;
        try {
            lock = RedissLockUtil.lock(lockKey);
            if (lock) {
                log.info("user:{} start reserve,time:{}", loginUser.getId(), DateUtil.now());
                R result =  chargerAppOrderService.startReserveOrder(orderDto, loginUser);
                return result;
            }
        } catch (Exception e) {
            log.info("reserve error:{}", e);
        } finally {
            if (lock) {
                RedissLockUtil.unlock(lockKey);
            }
        }
        return R.ok(ResponseTypeEnum.GLOBAL_REQUEST_FAILED);
    }

    /**
     * 获取预约订单实时数据
     */
    @SafeRead
    @Login
    @PostMapping("/order/getRealReserveOrderInfo")
    public R getRealReserveOrderInfo(@LoginUser TUser loginUser, @RequestParam("id") Integer orderId) {
        return chargerAppOrderService.getRealReserveOrderInfo(orderId);
    }

    /**
     *停止预约
     */
    @Login
    @PostMapping("/order/stopReserve")
    public R stopReserve(@RequestParam("id") Integer orderId, @LoginUser TUser loginUser) {
        String lockKey = new StringBuilder(ChargerAppConstants.REDIS_LOCK_APP_CANCEL_RESERVE).append(orderId).toString();
        Boolean lock = Boolean.FALSE;
        try {
            lock = RedissLockUtil.lock(lockKey);
            if (lock) {
                log.info("user:{} stop reserve,time:{}", loginUser.getId(), DateUtil.now());
                R result =  chargerAppOrderService.stopReserveOrder(orderId);
                return result;
            }
        } catch (Exception e) {
            log.info("stop reserve error:{}", e);
        } finally {
            if (lock) {
                RedissLockUtil.unlock(lockKey);
            }
        }
        return R.ok(ResponseTypeEnum.GLOBAL_REQUEST_FAILED);
    }

    /**
     *获取预约订单详情
     */
    @SafeRead
    @Login
    @PostMapping("/order/getReserveOrderInfo")
    public R getReserveOrderInfo(@LoginUser TUser loginUser, @RequestParam(value = "id") Integer orderId) {
        try {
            return remoteBusinessService.getReserveOrderInfo(orderId, SecurityConstants.FROM_IN);
        } catch (Exception e) {
            log.info("get reserve order info error:{}", e);
            return R.ok(ResponseTypeEnum.GLOBAL_REQUEST_FAILED);
        }
    }

    /**
     *获取未支付充电订单列表
     */
    @Login
    @PostMapping("/order/getUnpayOrderList")
    public R getUnpayOrderList(@LoginUser TUser loginUser) {
        try {
			QueryChargerOrderEntity entity = new QueryChargerOrderEntity();
			entity.setAppId(loginUser.getId());
            return remoteBusinessService.getChargingOrderList(entity, SecurityConstants.FROM_IN);
        } catch (Exception e) {
            log.info("get unpaid order error:{}", e);
            return R.ok(ResponseTypeEnum.GLOBAL_REQUEST_FAILED);
        }
    }

    /**
     *获取预约中的订单列表
     */
    @Login
    @PostMapping("/order/getReservingOrderList")
    public R getReservingOrderList(@LoginUser TUser loginUser) {
        try {
            QueryChargerOrderEntity query = new QueryChargerOrderEntity();
            query.setAppId(loginUser.getId());
            return remoteBusinessService.getReservingOrderList(query, SecurityConstants.FROM_IN);
        } catch (Exception e) {
            log.info("get reserving order error:{}", e);
            return R.ok(ResponseTypeEnum.GLOBAL_REQUEST_FAILED);
        }
    }

    /**
     *获取充电订单列表
     */
    @Login
    @PostMapping("/order/getChargeOrderList")
    public R getChargeOrderList(@Valid @RequestBody QueryChargerOrderEntity entity, @LoginUser TUser loginUser) {
        try {
            entity.setAppId(loginUser.getId());
            if (StrUtil.isNotBlank(entity.getEndTime())) {
                entity.setEndTime(entity.getEndTime() + " 23:59:59");
            }
            return remoteBusinessService.getChargeOrderList(entity, SecurityConstants.FROM_IN);
        } catch (Exception e) {
            log.info("get charging order error:{}", e);
            return R.ok(ResponseTypeEnum.GLOBAL_REQUEST_FAILED);
        }
    }

    /**
     *获取预约订单列表
     */
    @Login
    @PostMapping("/order/getReserveOrderList")
    public R getReserveOrderList(@Valid @RequestBody QueryChargerOrderEntity entity, @LoginUser TUser loginUser) {
        try {
            entity.setAppId(loginUser.getId());
            if (StrUtil.isNotBlank(entity.getEndTime())) {
                entity.setEndTime(entity.getEndTime() + " 23:59:59");
            }
            return remoteBusinessService.getReserveOrderList(entity, SecurityConstants.FROM_IN);
        } catch (Exception e) {
            log.info("get reserve order error:{}", e);
            return R.ok(ResponseTypeEnum.GLOBAL_REQUEST_FAILED);
        }
    }

    /**
     * 获取用户排队订单列表
     */
    @Login
    @PostMapping("/order/getWaitOrderList")
    public R getWaitOrderList(@LoginUser TUser loginUser) {
        return remoteBusinessService.getWaitOrderList(loginUser.getId(), SecurityConstants.FROM_IN);
    }

    @Login
	@PostMapping("/order/export")
	public R chargerOrderExport(@RequestBody QueryChargerAppOrderEntity entity, @LoginUser TUser loginUser) {
		String lockKey = new StringBuilder(ChargerAppConstants.REDIS_LOCK_APP_CHARGER_ORDER_EXPORT).append(loginUser.getId()).toString();
		Boolean lock = Boolean.FALSE;
		try {
			entity.setAppId(loginUser.getId());
			lock = RedissLockUtil.lock(lockKey);
			if (lock) {
				//log.info("user:{} export charging order:{}", loginUser.getId(), JSON.toJSONString(entity));
				R result =  remoteFileUploadService.exportAppOrder(entity,SecurityConstants.FROM_IN);
				if(ObjectUtil.isNull(result.getData()))
				{
					result = R.ok(ResponseTypeEnum.APP_CHARGER_EXPORT_EMPTY);
				}
				return result;
			}
		} catch (Exception e) {
			log.info("export charging order error:{}", e);
		} finally {
			if (lock) {
				RedissLockUtil.unlock(lockKey);
			}
		}

		return R.ok(ResponseTypeEnum.GLOBAL_REQUEST_FAILED);
	}

    /**
     * 用户取消排队订单
     */
    @Login
    @PostMapping("/order/cancelWaitOrder")
    public R cancelWaitOrder(@LoginUser TUser loginUser, @RequestParam("id") Integer orderId) {
        return remoteBusinessService.cancelWaitOrder(orderId, loginUser.getId(), SecurityConstants.FROM_IN);
    }

    /**----------------------用户车辆相关接口------------------------------------*/
    /**
     *新增车辆
     */
    @Login
    @PostMapping("/veh/addVehicle")
    public R addVehicle(@Valid @RequestBody TUserVehicle entity, @LoginUser TUser loginUser) {
        return chargerAppVehicleService.addVehicle(entity, loginUser);
    }

    /**
     *修改车辆
     */
    @Login
    @PostMapping("/veh/updateVehicle")
    public R updateVehicle(@Valid @RequestBody TUserVehicle entity, @LoginUser TUser loginUser) {
        return chargerAppVehicleService.updateVehicle(entity, loginUser);
    }

    /**
     *车辆详情
     */
    @SafeRead
    @Login
    @PostMapping("/veh/vehicleInfo")
    public R getVehicleInfo(@LoginUser TUser loginUser, @RequestParam("id") Integer id) {
        return chargerAppVehicleService.getVehicleInfo(id);
    }

    /**
     *删除车辆
     */
    @Login
    @PostMapping("/veh/delVehicle")
    public R delVehicle(@LoginUser TUser loginUser, @RequestParam("id") Integer id) {
        return chargerAppVehicleService.delVehicle(loginUser.getId(), id);
    }

    /**
     *分页获取车辆列表
     */
    @Login
    @PostMapping("/veh/vehicleList")
    public R getVehicleList(@Valid @RequestBody QueryVehicleListDto entity, @LoginUser TUser loginUser) {
        return chargerAppVehicleService.getVehicleList(entity, loginUser);
    }


    /**----------------------非运营设备相关接口------------------------------------*/

    /**
     * 获取家桩资产信息
     * @param scanDto 扫码内容:可以是桩编码或者桩编码&枪编号
     * @return
     */
    @Login
    @PostMapping("/device/getHomePileInfoByCode")
    public R getHomePileInfoByCode(@RequestBody ChargerScanDto scanDto) {
        return chargerAppDeviceService.getHomePileInfoByCode(scanDto);
    }

    /**
     * 绑定家桩
     */
    @Login
    @PostMapping("/device/bindHomePile")
    public R bindHomePile(@Valid @RequestBody ChargerPersonalPileDto dto, @LoginUser TUser loginUser) {
        return chargerAppDeviceService.bindHomePile(dto, loginUser);
    }

    /**
     * 解绑家桩，解绑即删除
     */
    @Login
    @PostMapping("/device/delHomePile")
    public R delHomePile(@RequestParam("pileId") Integer pileId, @LoginUser TUser loginUser) {
        Integer ownerId = chargerRedisService.getHomePileOwnerId(pileId);
        if (ObjectUtil.isNotNull(ownerId) && ownerId.equals(loginUser.getId())) {
           return chargerAppDeviceService.delHomePile(pileId);
        }
        return R.ok(ResponseTypeEnum.APP_NOT_HOME_PILE_OWNER);
    }

    /**
     * 修改家桩
     */
    @Login
    @PostMapping("/device/updateHomePile")
    public R updateHomePile(@Valid @RequestBody ChargerPersonalPileDto dto, @LoginUser TUser loginUser) {
        Integer ownerId = chargerRedisService.getHomePileOwnerId(dto.getId());
        if (ObjectUtil.isNotNull(ownerId) && ownerId.equals(loginUser.getId())) {
            R result = remoteBusinessService.updatePersonalPile(dto, SecurityConstants.FROM_IN);
            return result;
        }
        return R.ok(ResponseTypeEnum.APP_NOT_HOME_PILE_OWNER);
    }

    /**
     * 获取家桩列表
     */
    @Login
    @PostMapping("/device/getHomePileList")
    public R getHomePileList(@Valid @RequestBody QueryPersonalPileEntity dto, @LoginUser TUser loginUser) {
        dto.setUserId(loginUser.getId());
       return remoteBusinessService.getUserPersonalPile(dto, SecurityConstants.FROM_IN);
    }

    /**
     * 获取家桩使用人列表
     */
    @SafeRead
    @Login
    @PostMapping("/device/getHomePileUserList")
    public R getHomePileUserList(@LoginUser TUser loginUser, @RequestParam("id") Integer pileId) {
        return remoteBusinessService.getPersonalPileUserList(pileId, SecurityConstants.FROM_IN);
    }

    /**
     * 根据邮箱获取用户信息
     */
    @Login
    @PostMapping("/device/getUserInfoByName")
    public R getUserInfoByEmail(@RequestParam("name") String email, @LoginUser TUser loginUser) {
        return chargerAppDeviceService.getUserInfoByEmail(email, loginUser.getPayUuid());
    }

    /**
     * 为家桩绑定共同使用人
     */
    @Login
    @PostMapping("/device/addHomePileUser")
    public R addHomePileUser(@Valid @RequestBody ChargerPilePersonalEntity dto, @LoginUser TUser loginUser) {
        Integer ownerId = chargerRedisService.getHomePileOwnerId(dto.getPileId());
        if (ObjectUtil.isNotNull(ownerId) && ownerId.equals(loginUser.getId())) {
            return chargerAppDeviceService.addHomePileUser(dto);
        }
        return R.ok(ResponseTypeEnum.APP_NOT_HOME_PILE_OWNER);
    }

    /**
     * 删除家桩共同使用人
     */
    @Login
    @PostMapping("/device/delHomePileUser")
    public R delHomePileUser(@RequestBody ChargerPilePersonalEntity dto, @LoginUser TUser loginUser) {
        Integer ownerId = chargerRedisService.getHomePileOwnerId(dto.getPileId());
        if (ObjectUtil.isNotNull(ownerId) && ownerId.equals(loginUser.getId())) {
            if (!ownerId.equals(dto.getUserId())) {
                return chargerAppDeviceService.delHomePileUser(dto);
            } else {
                return R.ok(ResponseTypeEnum.GLOBAL_REQUEST_FAILED);
            }
        }
        return R.ok(ResponseTypeEnum.APP_NOT_HOME_PILE_OWNER);
    }

    /**
     * 家桩预约充电规则设置
     */
    @Login
    @PostMapping("/device/setHomePileReserveRule")
    public R setHomePileReserveRule(@Valid @RequestBody ChargerPersonalPileReserveSettingEntity dto, @LoginUser TUser loginUser) {
        Integer ownerId = chargerRedisService.getHomePileOwnerId(dto.getPileId());
        if (ObjectUtil.isNotNull(ownerId) && ownerId.equals(loginUser.getId())) {
            String lockKey = new StringBuilder(ChargerAppRedisConstants.REDIS_LOCK_APP_HOME_PILE_RESERVE_SETTING).append(loginUser.getId()).toString();
            Boolean lock = Boolean.FALSE;
            try {
                lock = RedissLockUtil.lock(lockKey);
                if (lock) {
                    log.info("user:{} set timing charge,time:{}", loginUser.getId(), DateUtil.now());
                    dto.setCreateBy(loginUser.getEmail());
                    dto.setCreateTime(DateUtil.now());
                    dto.setAppId(loginUser.getId());
                    R result = remoteBusinessService.setPersonalPileReserveRule(dto, SecurityConstants.FROM_IN);
                    return result;
                }
            } catch (Exception e) {
                log.info("set timing charge error:{}", e);
            } finally {
                if (lock) {
                    RedissLockUtil.unlock(lockKey);
                }
            }
            return R.ok(ResponseTypeEnum.GLOBAL_REQUEST_FAILED);
        }
        return R.ok(ResponseTypeEnum.APP_NOT_HOME_PILE_OWNER);
    }

    /**
     * 获取家桩预约充电规则数据
     */
    @Login
    @PostMapping("/device/getHomePileReserveRule")
    public R getHomePileReserveRule(@RequestParam("pileId") Integer pileId, @LoginUser TUser loginUser) {
        return remoteBusinessService.getPersonalPileReserveRule(pileId, loginUser.getId(), SecurityConstants.FROM_IN);
    }

    /**
     * 获取家桩预约充电规则绑定结果
     */
    @Login
    @PostMapping("/device/getHomePileReserveRuleBingdResult")
    public R getHomePileReserveRuleBingdResult(@RequestParam("pileId") Integer pileId, @LoginUser TUser loginUser) {
        return chargerAppDeviceService.getHomePileReserveRuleBingdResult(pileId, loginUser.getId());
    }

    /**
     * 蓝牙设置家桩预约规则
     */
    @Login
    @PostMapping("/device/blueSetReserveRuleCommit")
    public R blueSetReserveRuleCommit(@Valid @RequestBody ChargerPersonalPileReserveSettingEntity dto, @LoginUser TUser loginUser) {
        Integer ownerId = chargerRedisService.getHomePileOwnerId(dto.getPileId());
        if (ObjectUtil.isNotNull(ownerId) && ownerId.equals(loginUser.getId())) {
            String lockKey = new StringBuilder(ChargerAppRedisConstants.REDIS_LOCK_APP_HOME_PILE_RESERVE_SETTING).append(loginUser.getId()).toString();
            Boolean lock = Boolean.FALSE;
            try {
                lock = RedissLockUtil.lock(lockKey);
                if (lock) {
                    dto.setCreateBy(loginUser.getEmail());
                    dto.setCreateTime(DateUtil.now());
                    dto.setAppId(loginUser.getId());
                    R result =  remoteBusinessService.blueSetReserveRuleCommit(dto, SecurityConstants.FROM_IN);
                    return result;
                }
            } catch (Exception e) {
                log.info("bluebooth set timing charge error:{}", e);
            } finally {
                if (lock) {
                    RedissLockUtil.unlock(lockKey);
                }
            }
            return R.ok(ResponseTypeEnum.GLOBAL_REQUEST_FAILED);
        }
        return R.ok(ResponseTypeEnum.APP_NOT_HOME_PILE_OWNER);
    }

    /**-----------------------------运营设备相关接口-------------------------------------------------------*/

    /**
     * 获取站点地图页
     */
    @ChargerApp(filter = true)
    @PostMapping("/device/getStationListByMap")
    public R getStationListByMap(@RequestHeader("uuid") String uuid, @RequestBody QueryStationEntity entity) {
        return chargerAppDeviceService.getStationListByMap(entity, uuid);
    }

    /**
     * 获取站点列表
     */
    @ChargerApp(filter = true)
    @PostMapping("/device/getStationListBySearch")
    public R getStationListBySearch(@RequestHeader("uuid") String uuid, @RequestBody QueryStationEntity entity) {
        return chargerAppDeviceService.getStationListBySearch(entity, uuid);
    }

    /**
     * 查询站桩详情
     */
    @PostMapping("/device/getStationDetails")
    public R getStationDetails(@Valid @RequestBody QueryStationDetailEntity entity) {
        return chargerAppDeviceService.getStationDetails(entity);
    }

    /**
     * 查询设备状态接口
     */
    @Login
    @PostMapping("/device/getDeviceStatus")
    public R getDeviceStatus(@RequestParam("pileSn") String pileSn, @RequestParam(value = "gunNo", required = false) Integer gunNo) {
        return chargerAppUserService.getDeviceStatus(pileSn, gunNo);
    }

    /**
     * 查询站点详情
     */
    @Login
    @PostMapping("/device/getStationInfoSimple")
    public R getStationInfoSimple(@RequestParam("stationId") Integer stationId, @LoginUser TUser loginUser) {
        return chargerAppDeviceService.getStationInfoSimple(stationId, loginUser);
    }

    /**
     * 查询站点列表(分页)
     */
    @Login
    @PostMapping("/device/getStationListWithPeriod")
    @ChargerApp(filter = true)
    public R getStationListWithPeriod(@Valid @RequestBody QueryStationEntity entity, @LoginUser TUser loginUser) {
        return chargerAppDeviceService.getStationListWithPeriod(entity, loginUser);
    }

    /**
     * 查询站桩详情
     */
    @Login
    @PostMapping("/device/getStationPileInfo")
    public R getStationPileInfo(@RequestParam("stationId") Integer stationId) {
        return remoteBusinessService.getStationPileInfo(stationId, SecurityConstants.FROM_IN);
    }

    /**
     * 查询桩的枪列表和枪状态
     */
    @Login
    @PostMapping("/device/getGunInfoStatus")
    public R getGunInfoStatus(@RequestParam("pileId") Integer pileId) {
        return remoteBusinessService.getGunInfoStatus(pileId, SecurityConstants.FROM_IN);
    }

    /**
     * 查询枪详情
     */
    @Login
    @PostMapping("/device/getGunDetail")
    public R getGunDetail(@RequestParam("gunId") Integer gunId) {
        return remoteBusinessService.getGunDetail(gunId, SecurityConstants.FROM_IN);
    }

    /**----------------------卡号相关接口------------------------------------*/

    /**
     * app 绑定卡号
     */
    @Login
    @PostMapping("/card/appBindCard")
    public R appBindCard(@RequestParam("cardSn") String cardSn ,@LoginUser TUser loginUser) {
        R result = chargerAppUserService.appBindCard(cardSn,loginUser);
        return result;
    }

    /**
     * app 挂失卡号
     */
    @Login
    @PostMapping("/card/appLossReportCard")
    public R appLossReportCard(@RequestParam("cardSn") String cardSn, @LoginUser TUser loginUser) {
        R result = chargerAppUserCardService.appLossReportCard(cardSn, loginUser);
        return result;
    }

    @Login
    @PostMapping("/card/appCancelLossReportCard")
    public R appCancelLossReportCard(@RequestParam("cardSn")String cardSn,@LoginUser TUser loginUser)
    {
        R result = chargerAppUserCardService.appCancelLossReportCard(cardSn,loginUser);
        return result;
    }

    /**
     * app 删除卡号
     */
    @Login
    @PostMapping("/card/del")
    public R appDelCard(@LoginUser TUser loginUser, @RequestParam("cardId") Integer cardId ) {
        TUserCard card = chargerAppUserCardService.getOne(new QueryWrapper<TUserCard>().eq("card_id", cardId)
                .eq("user_id", loginUser.getId()).last("limit 1"));
        if (ObjectUtil.isNotNull(card)) {
            R resp = remoteBusinessService.appDelCard(cardId,SecurityConstants.FROM_IN);
            if (0 == resp.getCode())
            {
                return R.ok();
            }
            return resp;
        }
        return R.ok(ResponseTypeEnum.GLOBAL_REQUEST_FAILED);
    }

    /**
     *查询用户卡列表
     */
    @Login
    @PostMapping("/card/getCardList")
    public R getCardList(@LoginUser TUser loginUser) {
        return chargerAppUserCardService.getCardList(loginUser.getId());
    }

    @Login
    @PostMapping("/addEnergyBox")
    public R addEnergyBox(@Valid @RequestBody ChargerEnergyBoxEntity entity, @LoginUser TUser loginUser) {
        R result = chargerAppEnergyBoxService.addEnergyBox(entity, loginUser);
        return result;
    }

    @Login
    @PostMapping("/delEnergyBox")
    public R delEnergyBox(@LoginUser TUser loginUser, @RequestParam("boxId") Integer boxId) {
        R result = remoteBusinessService.delEnergyBox(boxId, loginUser.getId(), SecurityConstants.FROM_IN);
        return result;
    }

    @Login
    @PostMapping("/updateEnergyBox")
    public R updateEnergyBox(@Valid @RequestBody ChargerEnergyBoxEntity entity) {
        R result = chargerAppEnergyBoxService.updateEnergyBox(entity);
        return result;
    }

    @Login
    @PostMapping("/getBoxListByUserId")
    public R getBoxListByUserId(@LoginUser TUser loginUser) {
        return remoteBusinessService.getBoxListByUserId(loginUser.getId(), SecurityConstants.FROM_IN);
    }

    @Login
    @PostMapping("/getPileListByBoxId")
    public R getPileListByBoxId(@RequestParam("boxId") Integer boxId, @LoginUser TUser loginUser) {
        return remoteBusinessService.getPileListByBoxId(boxId, loginUser.getId(), SecurityConstants.FROM_IN);
    }

    @SafeRead
    @Login
    @PostMapping("/getBoxPileList")
    public R getBoxPileList(@LoginUser TUser loginUser, @RequestParam("boxId") Integer boxId) {
        return remoteBusinessService.getBoxPileList(boxId, SecurityConstants.FROM_IN);
    }

    @SafeRead
    @Login
    @PostMapping("/getBoxInfoByPileId")
    public R getBoxInfoByPileId(@LoginUser TUser loginUser, @RequestParam("pileId") Integer pileId) {
        return remoteBusinessService.getBoxInfoByPileId(pileId, SecurityConstants.FROM_IN);
    }

    @Login
    @PostMapping("/boxBindPile")
    public R boxBindPile(@Valid @RequestBody ChargerEnergyBoxBindPileEntity entity) {
        R result = remoteBusinessService.boxBindPile(entity, SecurityConstants.FROM_IN);
        return result;
    }

    @Login
    @PostMapping("/setDynamicPower")
    public R setDynamicPower(@Valid @RequestBody ChargerDynamicPowerCtrlEntity entity, @LoginUser TUser loginUser) {
        R result = chargerAppEnergyBoxService.setDynamicPower(entity, loginUser);
        return result;
    }

    @Login
    @PostMapping("/getDynamicPowerResult")
    public R getDynamicPowerResult(@RequestParam("id") Integer id) {
        return chargerAppEnergyBoxService.getDynamicPowerResult(id);
    }

    @Login
    @PostMapping("/saveEmsPara")
    public R saveEmsPara(@Valid @RequestBody ChargerEmsParaDto entity) {
        return remoteBusinessService.saveEmsPara(entity, SecurityConstants.FROM_IN);
    }

    @Login
	@PostMapping("/addProfile")
	public R addProfile(@Valid @RequestBody ChargerAppChargingProfileEntity entity, @LoginUser TUser loginUser){
		entity.setAppId(loginUser.getId());
		entity.setAppName(loginUser.getEmail());
		R result = remoteBusinessService.addProfile(entity, SecurityConstants.FROM_IN);
		return result;
	}

    @Login
    @PostMapping("/addBindProfile")
    public R addBindProfile(@Valid @RequestBody ChargerAppChargingNewProfileEntity entity, @LoginUser TUser loginUser){
        entity.setAppId(loginUser.getId());
        entity.setAppName(loginUser.getEmail());
        R result = remoteBusinessService.addBindProfile(entity, SecurityConstants.FROM_IN);
        return result;
    }


    @Login
    @PostMapping("/updateProfile")
    public R updateProfile(@Valid @RequestBody ChargerAppChargingProfileEntity entity, @LoginUser TUser loginUser){
        entity.setAppId(loginUser.getId());
        entity.setAppName(loginUser.getEmail());
        R result = remoteBusinessService.updateProfile(entity, SecurityConstants.FROM_IN);
        return result;
    }

    @Login
    @PostMapping("/updatePileProfile")
    public R updatePileProfile(@Valid @RequestBody ChargerAppChargingNewProfileEntity entity, @LoginUser TUser loginUser){
        entity.setAppId(loginUser.getId());
        entity.setAppName(loginUser.getEmail());
        R result = remoteBusinessService.updatePileProfile(entity, SecurityConstants.FROM_IN);
        return result;
    }

	@Login
	@PostMapping("/delProfile")
	public R delProfile(@RequestParam("id")Integer id, @LoginUser TUser loginUser){
    	ChargerAppChargingProfileEntity entity = new ChargerAppChargingProfileEntity();
		entity.setId(id);
		entity.setAppId(loginUser.getId());
		entity.setAppName(loginUser.getEmail());
		R result = remoteBusinessService.delProfile(entity,SecurityConstants.FROM_IN);
		return result;
	}

	@Login
	@PostMapping("/infoProfile")
	public R infoProfile(@RequestParam("id")Integer id, @LoginUser TUser loginUser){
		return remoteBusinessService.profileInfo(id, loginUser.getId(),SecurityConstants.FROM_IN);
	}

    @Login
    @PostMapping("/pileProfileInfo")
    public R pileProfileInfo(@RequestParam("id")Integer id, @LoginUser TUser loginUser){
        return remoteBusinessService.pileProfileInfo(id, loginUser.getId(),SecurityConstants.FROM_IN);
    }

	@Login
	@PostMapping("/listProfile")
	public R listProfile(@Valid @RequestBody QueryPersonalPileEntity entity, @LoginUser TUser loginUser){
		entity.setUserId(loginUser.getId());
		return remoteBusinessService.listProfile(entity,SecurityConstants.FROM_IN);
	}

	@Login
	@PostMapping("/bindList")
	public R bindList(@RequestParam("id")Integer id, @LoginUser TUser loginUser){
		return remoteBusinessService.bindList(id,loginUser.getId(),SecurityConstants.FROM_IN);
	}

	@Login
	@PostMapping("/bindProfile")
	public R bindProfile(@Valid @RequestBody ChargerAppBlueProfileBindCommitEntity entity, @LoginUser TUser loginUser){
		entity.setAppId(loginUser.getId());
		entity.setAppName(loginUser.getEmail());
		return remoteBusinessService.bindProfile(entity,SecurityConstants.FROM_IN);
	}

	@Login
	@PostMapping("/bindStatus")
	public R getBindStatus(@RequestParam("id")Integer id){
		return remoteBusinessService.getBindStatus(id,SecurityConstants.FROM_IN);
	}

	@Login
	@PostMapping("/blueBindCommit")
	public R blueBindCommit(@Valid @RequestBody ChargerAppBlueProfileBindCommitEntity entity, @LoginUser TUser loginUser){
		entity.setAppId(loginUser.getId());
		entity.setAppName(loginUser.getEmail());
		R result = remoteBusinessService.blueBindCommit(entity,SecurityConstants.FROM_IN);
		return result;
	}

    @Login
    @PostMapping("/blueNewBindCommit")
    public R blueNewBindCommit(@Valid @RequestBody ChargerAppNewBlueProfileBindCommitEntity entity, @LoginUser TUser loginUser){
        entity.setAppId(loginUser.getId());
        entity.setAppName(loginUser.getEmail());
        R result = remoteBusinessService.blueNewBindCommit(entity,SecurityConstants.FROM_IN);
        return result;
    }

    @Login
    @PostMapping("/delPileBindProfile")
    public R delPileBindProfile(@RequestBody ChargerAppChargingProfileEntity entity, @LoginUser TUser loginUser){
        entity.setAppId(loginUser.getId());
        entity.setAppName(loginUser.getEmail());
        R result = remoteBusinessService.delPileBindProfile(entity,SecurityConstants.FROM_IN);
        return result;
    }

    @Login
	@PostMapping("/credit/add")
	public R addCredit(@Valid @RequestBody ChargerAppCreditCardEntity entity,@LoginUser TUser loginUser)
	{
		entity.setUserId(loginUser.getId());
		return chargerAppCreditCardService.add(entity,loginUser.getPayUuid());
	}

	@Login
	@PostMapping("/credit/del")
	public R delCredit(@RequestParam("id")Integer id,@LoginUser TUser loginUser)
	{
		return chargerAppCreditCardService.del(id,loginUser.getId(),loginUser.getPayUuid());
	}

	@Login
	@PostMapping("/credit/info")
	public R creditInfo(@RequestParam("id")Integer id,@LoginUser TUser loginUser)
	{
		return chargerAppCreditCardService.info(id,loginUser.getId());
	}

	@Login
	@PostMapping("/credit/list")
	public R creditInfo(@LoginUser TUser loginUser)
	{
		return chargerAppCreditCardService.list(loginUser.getId());
	}
}
