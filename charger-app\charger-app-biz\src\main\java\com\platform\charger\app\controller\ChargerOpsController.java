package com.platform.charger.app.controller;

import cn.hutool.extra.servlet.ServletUtil;
import com.platform.charger.app.api.entity.TOpsUser;
import com.platform.charger.app.entity.ChargerOpsUserDto;
import com.platform.charger.app.entity.UpdatePwdDto;
import com.platform.charger.app.service.ChargerOpsUserService;
import com.platform.platform.common.core.util.R;
import com.platform.platform.common.security.annotation.Login;
import com.platform.platform.common.security.annotation.LoginUser;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;

@RestController
@AllArgsConstructor
@RequestMapping("/ops")
public class ChargerOpsController {
    private final ChargerOpsUserService chargerOpsUserService;

    /**
     * 运维用户账号密码登录
     */
    @PostMapping("/pwdLogin")
    public R pwdLogin(/*@RequestHeader("uuid") String payUuid,*/ @Valid @RequestBody ChargerOpsUserDto dto, HttpServletRequest request) {
        //dto.setPayUuid(payUuid);
        dto.setLoginSource(request.getHeader("user-agent"));
        dto.setLastIp(ServletUtil.getClientIP(request));
        return chargerOpsUserService.pwdLogin(dto);
    }

    /**
     * 运维用户修改密码
     */
    @Login
    @PostMapping("/updatePwd")
    public R updatePwd(@Valid @RequestBody UpdatePwdDto dto, @LoginUser TOpsUser loginUser) {
        return chargerOpsUserService.updatePwd(dto, loginUser);
    }
}
