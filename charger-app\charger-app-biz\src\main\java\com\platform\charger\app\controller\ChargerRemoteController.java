package com.platform.charger.app.controller;

import com.platform.charger.app.api.entity.*;
import com.platform.charger.app.service.*;
import com.platform.charger.business.api.entity.ChargerAppCreditPayEntity;
import com.platform.platform.common.core.util.R;
import com.platform.platform.common.security.annotation.Inner;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.math.BigDecimal;
import java.util.List;

@Slf4j
@Inner
@RestController
@AllArgsConstructor
@RequestMapping("/remote")
public class ChargerRemoteController {
    private final ChargerAppUserService chargerAppUserService;
    private final ChargerAppCollectService chargerAppCollectService;
    private final ChargerAppOrderStatisticsService chargerAppOrderStatisticsService;
    private final ChargerAppUserCardService chargerAppUserCardService;
    private final ChargerAppOrderService chargerAppOrderService;
    private final ChargerAppCreditCardService chargerAppCreditCardService;

    @PostMapping("/checkCredit")
	public R checkCredit(@RequestParam("userId") Integer userId)
	{
		return chargerAppCreditCardService.checkCredit(userId);
	}

    @PostMapping("/creditPay")
	public R creditPay(@RequestBody ChargerAppCreditPayEntity entity)
	{
		return R.ok(chargerAppOrderService.creditPayFromCard(entity));
	}

    @PostMapping("/getUserByShortIdIncludeBlock")
    public R getUserByShortIdIncludeBlock(@RequestParam("shortId") String shortId) {
        return chargerAppUserService.getUserByShortIdIncludeBlock(shortId);
    }

    @PostMapping("/getUserById")
    public R<TUser> getUserById(@RequestParam("id") Integer id) {
        return R.ok(chargerAppUserService.getById(id));
    }

    @PostMapping("/delStation")
    public R delStation(@RequestParam("stationId") Integer stationId) {
        return chargerAppCollectService.delStation(stationId);
    }

    /*@PostMapping("/payOrder")
    public R payOrder(@RequestBody ChargerOrderEntity orderEntity) {
        return chargerAppOrderService.payOrder(orderEntity);
    }*/

    @PostMapping("/getAppUserStatistics")
    public R getAppUserStatistics(@RequestBody QueryAppUserListDto dto) {
        return chargerAppUserService.getAppUserStatistics(dto);
    }

    /*@PostMapping("/payReserveOrder")
    public R payReserveOrder(@RequestBody ChargerReserveOrderEntity orderEntity) {
        return chargerAppOrderService.payReserveOrder(orderEntity);
    }*/

    @PostMapping("/delCard")
    public R delCard(@RequestParam("userId")Integer userId,@RequestParam("cardSn")String cardSn)
    {
        return chargerAppUserCardService.delCard(userId,cardSn);
    }

    @PostMapping("/saveAppChargerOrderStatistics")
    public void saveAppChargerOrderStatistics(@RequestBody List<TAppOrderStatistics> list) {
        chargerAppOrderStatisticsService.saveAppChargerOrderStatistics(list);
    }

    @PostMapping("/updateCardStatus")
    public void updateCardStatus(@RequestParam("cardSn")String cardSn, @RequestParam("status")Integer status)
    {
        chargerAppUserCardService.updateCardStatus(cardSn,status);
    }

    @PostMapping("/updateCardBalance")
    public void updateCardStatus(@RequestParam("cardSn")String cardSn, @RequestParam("balance") BigDecimal balance)
    {
        chargerAppUserCardService.updateCardBalance(cardSn,balance);
    }

    @PostMapping("/updateCardType")
    public void updateCardType(@RequestParam("cardSn")String cardSn, @RequestParam("type")Integer type )
    {
        chargerAppUserCardService.updateCardType(cardSn,type);
    }

    @PostMapping("/sendNotifyToUser")
    public void sendNotifyToUser(@RequestBody ChargerAppNotifyEntity entity) {
        chargerAppUserService.sendNotifyToUser(entity);
    }

    @Inner
    @PostMapping("/fleetsBindApp")
    public R fleetsBindApp(@Valid @RequestBody FleetsBindAppDto dto){
        return chargerAppUserService.fleetsBindApp(dto);
    }

    @Inner
    @PostMapping("/fleetsDelDriver")
    public R fleetsDelDriver(@RequestParam("driverId") Integer driverId){
        return chargerAppUserService.fleetsDelDriver(driverId);
    }

    @PostMapping("/getUserByEmail")
    public R<TUser> getUserByEmail(@RequestParam("email") String email, @RequestParam("uuid") String payUuid)
    {
        return chargerAppUserService.getUserByEmail(email, payUuid);
    }

}
