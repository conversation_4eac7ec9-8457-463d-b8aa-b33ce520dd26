package com.platform.charger.app.controller;

import com.google.gson.JsonSyntaxException;
import com.stripe.model.*;
import com.stripe.net.ApiResource;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import spark.Request;
import spark.Response;

/**
 * @Created with IDEA
 * @author:sunbaolin
 * @Date:2022/6/13
 * @Time:10:21
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/stripe/callback")
public class ChargerStripeController {
	public void payCallBack(Request request, Response response)
	{
		String payload = request.body();
		Event event = null;

		try {
			event = ApiResource.GSON.fromJson(payload, Event.class);
		} catch (JsonSyntaxException e) {
			// Invalid payload
			response.status(400);
			return ;
		}

		// Deserialize the nested object inside the event
		EventDataObjectDeserializer dataObjectDeserializer = event.getDataObjectDeserializer();
		StripeObject stripeObject = null;
		if (dataObjectDeserializer.getObject().isPresent()) {
			stripeObject = dataObjectDeserializer.getObject().get();
		} else {
			// Deserialization failed, probably due to an API version mismatch.
			// Refer to the Javadoc documentation on `EventDataObjectDeserializer` for
			// instructions on how to handle this case, or return an error here.
		}

		// Handle the event
		switch (event.getType()) {
			case "payment_intent.succeeded":
				PaymentIntent paymentIntent = (PaymentIntent) stripeObject;
				// Then define and call a method to handle the successful payment intent.
				// handlePaymentIntentSucceeded(paymentIntent);
				break;
			case "payment_method.attached":
				PaymentMethod paymentMethod = (PaymentMethod) stripeObject;
				// Then define and call a method to handle the successful attachment of a PaymentMethod.
				// handlePaymentMethodAttached(paymentMethod);
				break;
			// ... handle other event types
			default:
				System.out.println("Unhandled event type: " + event.getType());
		}

		response.status(200);
		return ;
	}
}
