package com.platform.charger.app.controller;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.platform.charger.app.api.entity.TUser;
import com.platform.charger.app.constants.ChargerAppConstants;
import com.platform.charger.app.entity.ChargerOrderDto;
import com.platform.charger.app.entity.ChargerTestEntity;
import com.platform.charger.app.mapper.ChargerAppUserMapper;
import com.platform.charger.app.service.ChargerAppOrderService;
import com.platform.charger.app.service.ChargerRedisService;
import com.platform.charger.business.api.feign.RemoteBusinessService;
import com.platform.platform.common.core.constant.SecurityConstants;
import com.platform.platform.common.core.constant.enums.ResponseTypeEnum;
import com.platform.platform.common.core.util.R;
import com.platform.platform.common.lock.util.RedissLockUtil;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

/**
 * @Created with IDEA
 * @author:sunbaolin
 * @Date:2023/3/28
 * @Time:11:06
 */
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/test")
public class ChargerTestController {
	private final ChargerAppUserMapper chargerAppUserMapper;
	private final ChargerAppOrderService chargerAppOrderService;
	private final RemoteBusinessService remoteBusinessService;
	private final ChargerRedisService chargerRedisService;
	@PostMapping("/startCharge")
	public R startCharge(@Valid @RequestBody ChargerTestEntity entity)
	{
		TUser user = chargerRedisService.getUserInfoByEmail(entity.getEmail());
		if(ObjectUtil.isNotNull(user))
		{
			try {
				log.info("user:{} start charge:{},time:{}", user.getId(), JSON.toJSONString(entity), DateUtil.now());
				ChargerOrderDto dto = new ChargerOrderDto();
				BeanUtil.copyProperties(entity,dto,Boolean.FALSE);
				R result = chargerAppOrderService.testStartChargerOrder(dto, user);
				//3ds验证需要返回一个key值
				return result;
			} catch (Exception e) {
				log.info("start charge error:{}", e);
			}
		}

		return R.ok(ResponseTypeEnum.GLOBAL_REQUEST_FAILED);
	}

	@PostMapping("/getMeterValue")
	public R getMeterValue(@RequestParam("id")Integer id)
	{
		return chargerAppOrderService.getRealOrderInfo(id);
	}

	@PostMapping("/stopCharge")
	public R stopCharge(@RequestParam("id")Integer id)
	{
		String lockKey = new StringBuilder(ChargerAppConstants.REDIS_LOCK_APP_STOP_CHARGE).append(id).toString();
		Boolean lock = Boolean.FALSE;
		try {
			lock = RedissLockUtil.lock(lockKey);
			if (lock) {
				R result = chargerAppOrderService.stopChargeOrder(id);
				return result;
			}
		} catch (Exception e) {
			log.info("stop charge error:{}", e);
		} finally {
			if (lock) {
				RedissLockUtil.unlock(lockKey);
			}
		}
		return R.ok(ResponseTypeEnum.GLOBAL_REQUEST_FAILED);
	}

	@PostMapping("/getOrderInfo")
	public R getOrderInfo(@RequestParam("id")Integer id)
	{
		return remoteBusinessService.getChargeOrderInfo(id, SecurityConstants.FROM_IN);
	}
}
