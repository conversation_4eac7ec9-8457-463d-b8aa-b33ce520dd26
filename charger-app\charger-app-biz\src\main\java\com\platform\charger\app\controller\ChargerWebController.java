package com.platform.charger.app.controller;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.platform.charger.app.annotation.ChargerApp;
import com.platform.charger.app.api.entity.QueryAppUserListDto;
import com.platform.charger.app.api.entity.QueryAppUserStatisticsDto;
import com.platform.charger.app.api.entity.TUserCard;
import com.platform.charger.app.entity.*;
import com.platform.charger.app.service.*;
import com.platform.charger.business.api.entity.QueryAppOrderStatisticsDto;
import com.platform.charger.business.api.entity.QueryChargerOrderEntity;
import com.platform.charger.business.api.feign.RemoteBusinessService;
import com.platform.platform.common.core.constant.SecurityConstants;
import com.platform.platform.common.core.util.R;
import lombok.AllArgsConstructor;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

@RestController
@AllArgsConstructor
@RequestMapping("/web")
public class ChargerWebController {
    private final ChargerOpsUserService chargerOpsUserService;
    private final ChargerAppUserService chargerAppUserService;
    private final ChargerAppOrderStatisticsService chargerAppOrderStatisticsService;
    private final RemoteBusinessService remoteBusinessService;
    private final ChargerAppUserCardService chargerAppUserCardService;
    private final ChargerAppVersionService chargerAppVersionService;
    private final ChargerUserFeedbackService chargerUserFeedbackService;
    private final ChargerAppUserStatisticsService chargerAppUserStatisticsService;
    private final ChargerAppDeviceService chargerAppDeviceService;

    /***************************************平台运维用户相关接口*****************************/
    /**
     *添加运维用户
     */
    @PostMapping("/ops/addUser")
    @PreAuthorize("@pms.hasPermission('sys_opsuser_add')")
    @ChargerApp(agent = true)
    public R addOpsUser(@Valid @RequestBody ChargerOpsUserDto dto) {
        return chargerOpsUserService.addOpsUser(dto);
    }

    /**
     * 修改运维用户
     */
    @PostMapping("/ops/updateUser")
    @PreAuthorize("@pms.hasPermission('sys_opsuser_edit')")
    @ChargerApp(agent = true)
    public R updateOpsUser(@Valid @RequestBody ChargerOpsUserUpdateDto dto) {
        return chargerOpsUserService.updateOpsUser(dto);
    }

    /**
     * 删除运维用户
     */
    @PostMapping("/ops/delUser")
    @PreAuthorize("@pms.hasPermission('sys_opsuser_del')")
    @ChargerApp(agent = true)
    public R delOpsUser(@RequestParam("id") Integer id) {
        return chargerOpsUserService.delOpsUser(id);
    }

    /**
     * 查看运维用户详情
     */
    @PostMapping("/ops/userInfo")
    @PreAuthorize("@pms.hasPermission('sys_opsuser_detail')")
    public R getOpsUserInfo(@RequestParam("id") Integer id) {
        return chargerOpsUserService.getOpsUserInfo(id);
    }

    /**
     * 启/禁运维用户
     */
    @PostMapping("/ops/lockUser")
    @PreAuthorize("@pms.hasPermission('sys_opsuser_lock')")
    @ChargerApp(agent = true)
    public R lockOpsUser(@RequestParam("id") Integer id) {
        return chargerOpsUserService.lockOpsUser(id);
    }

    /**
     * 分页获取运维用户列表
     */
    @PostMapping("/ops/userList")
    @PreAuthorize("@pms.hasPermission('sys_opsuser_mgr')")
    public R getOpsUserList(@Valid @RequestBody QueryOpsUserListDto dto) {
        return chargerOpsUserService.getOpsUserList(dto);
    }

    /***************************************平台运营用户相关接口*****************************/

    /**
     * 分页获取运营用户列表
     */
    @PostMapping("/app/userList")
    @PreAuthorize("@pms.hasPermission('sys_appuser_mgr')")
    public R getAppUserList(@Valid @RequestBody QueryAppUserListDto dto) {
        return chargerAppUserService.getAppUserList(dto);
    }

    /**
     * 获取运营用户详情
     */
    @PostMapping("/app/userInfo")
    @PreAuthorize("@pms.hasPermission('sys_appuser_detail')")
    public R getAppUserInfo(@RequestParam("id") Integer userId) {
        return chargerAppUserService.getAppUserInfo(userId);
    }

    /**
     * 启/禁运营用户
     */
    @PostMapping("/app/lockUser")
    @PreAuthorize("@pms.hasPermission('sys_appuser_lock')")
    @ChargerApp(agent = true)
    public R lockAppUser(@RequestParam("id") Integer id) {
        return chargerAppUserService.lockAppUser(id);
    }

    /**
     * 删除运营用户
     */
    @PostMapping("/app/delUser")
    public R delAppUser(@RequestParam("id") Integer id) {
        return chargerAppUserService.delAppUser(id);
    }

    /**
     * 运营用户统计
     */
    @PostMapping("/app/getAppUserStatistics")
    public R getAppUserStatistics(@RequestBody QueryAppUserListDto dto) {
        return chargerAppUserService.getAppUserStatistics(dto);
    }

    /**
     * app用户充电订单统计
     */
    @PostMapping("/app/getChargerOrderStatistics")
	@PreAuthorize("@pms.hasPermission('sys_appuser_orderstats')")
    public R getAppChargerOrderStatistics(@RequestBody QueryAppOrderStatisticsDto dto) {
        return chargerAppOrderStatisticsService.getAppChargerOrderStatistics(dto);
    }

    /**
     * app用户充电订单记录
     */
    @PostMapping("/app/getChargeOrderList")
	@PreAuthorize("@pms.hasPermission('sys_appuser_order')")
    public R getChargeOrderList(@Valid @RequestBody QueryChargerOrderEntity dto) {
        if (StrUtil.isNotBlank(dto.getEndTime())) {
            dto.setEndTime(dto.getEndTime() + " 23:59:59");
        }
        return remoteBusinessService.getChargeOrderList(dto, SecurityConstants.FROM_IN);
    }

    /**
     * app用户充电订单详情
     */
    @PostMapping("/app/getChargeOrderInfo")
	@PreAuthorize("@pms.hasPermission('sys_appuser_orderdetail')")
    public R getChargerOrderInfo(@RequestParam("id") Integer orderId) {
        return remoteBusinessService.getChargeOrderInfo(orderId, SecurityConstants.FROM_IN);
    }

    /**
     * 运营用户卡列表
     */
    @PostMapping("/app/getCardList")
	@PreAuthorize("@pms.hasPermission('sys_appuser_card')")
    public R getCardList(@Valid @RequestBody QueryVehicleListDto dto) {
        QueryWrapper<TUserCard> queryWrapper = new QueryWrapper<TUserCard>().eq("user_id", dto.getUserId());
        Page page = new Page();
        page.setCurrent(dto.getCurrent());
        page.setSize(dto.getSize());
        return R.ok(chargerAppUserCardService.page(page, queryWrapper));
    }

    /**
     * 解绑家桩
     */
    @PostMapping("/app/delHomePile")
    @ChargerApp(agent = true)
    @PreAuthorize("@pms.hasPermission('sys_del_homePile')")
    public R delHomePile(@RequestParam("pileId") Integer pileId) {
        return chargerAppDeviceService.delHomePile(pileId);
    }

    /***************************************平台APP版本信息相关接口*****************************/
    /**
     * 新增APP版本信息
     */
    @PostMapping("/version/add")
    @PreAuthorize("@pms.hasPermission('sys_appversion_add')")
    @ChargerApp(agent = true)
    public R addVersion(@Valid @RequestBody AppVersionEntity entity) {
        return chargerAppVersionService.add(entity);
    }

    /**
     * 修改APP版本信息
     */
    @PostMapping("/version/update")
    @PreAuthorize("@pms.hasPermission('sys_appversion_edit')")
    @ChargerApp(agent = true)
    public R updateVersion(@Valid @RequestBody AppVersionEntity entity) {
        return chargerAppVersionService.update(entity);
    }

    /**
     * 查询APP版本信息
     */
    @PostMapping("/version/info")
    @PreAuthorize("@pms.hasPermission('sys_appversion_detail')")
    public R getVersionInfo(@RequestParam("id") Integer id) {
        return chargerAppVersionService.info(id);
    }

    /**
     * 删除APP版本信息
     */
    @PostMapping("/version/del")
    @PreAuthorize("@pms.hasPermission('sys_appversion_del')")
    @ChargerApp(agent = true)
    public R delVersion(@RequestParam("id") Integer id) {
        return chargerAppVersionService.del(id);
    }

    /**
     * APP版本信息列表
     */
    @PostMapping("/version/list")
    @PreAuthorize("@pms.hasPermission('sys_appversion_mgr')")
    public R getVersionList(@Valid @RequestBody QueryAppVersionListDto dto) {
        return chargerAppVersionService.list(dto);
    }

    /***************************************平台APP用户反馈相关接口*****************************/

    /**
     * 获取用户反馈内容列表
     */
    @PostMapping("/feedback/list")
    @PreAuthorize("@pms.hasPermission('sys_feedback_mgr')")
    public R getFeedBackList(@Valid @RequestBody QueryChargerOrderEntity dto) {
        return chargerUserFeedbackService.getFeedBackList(dto);
    }

    /**
     * 获取用户反馈内容详情
     */
    @PostMapping("/feedback/info")
    @PreAuthorize("@pms.hasPermission('sys_feedback_detail')")
    public R getFeedBackInfo(@RequestParam("id") Integer id) {
        return chargerUserFeedbackService.getFeedBackInfo(id);
    }

    /***************************************平台APP用户统计相关接口*****************************/
    /**
     * APP数字统计接口
     */
    @PostMapping("/statistics/digital")
    public R getDigitalData(@RequestBody QueryAppUserStatisticsDto dto) {
        return chargerAppUserStatisticsService.getDigitalData(dto);
    }

    /**
     * APP柱形统计接口
     */
    @PostMapping("/statistics/column")
    public R getColumnData(@RequestBody QueryAppUserStatisticsDto dto) {
        return chargerAppUserStatisticsService.getColumnData(dto);
    }

    /**
     * APP饼状统计接口
     */
    @PostMapping("/statistics/pie")
    public R getPieData(@RequestBody QueryAppUserStatisticsDto dto) {
        return chargerAppUserStatisticsService.getPieData(dto);
    }
}
