package com.platform.charger.app.entity;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.platform.platform.common.core.constant.ValidMessageConstants;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;

@Data
public class ChargerOpsUserUpdateDto {
    private Integer id;
    /**
     * 用户名
     */
    @NotBlank(message = ValidMessageConstants.APP_USER_NAME_NOT_BLANK)
    @Length(min = 1, max = 32, message = ValidMessageConstants.APP_USER_NAME_TOO_LONG + ",{min},{max}")
    private String userName;


    /**
     * 手机号码
     */
    @TableField(insertStrategy = FieldStrategy.IGNORED, updateStrategy = FieldStrategy.IGNORED)
    @Length(max = 32, message = ValidMessageConstants.APP_USER_PHONE_TOO_LONG + ",{max}")
    private String phone;

    /**
     * 账号过期时间
     */
    private String expireTime;
}
