package com.platform.charger.app.entity;

import com.platform.platform.common.core.constant.ValidMessageConstants;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * @Created with IDEA
 * @author:sunbaolin
 * @Date:2022/8/5
 * @Time:17:37
 */
@Data
public class ChargerStripeSecureEntity {

	/**
	 *PaymentIntent ID
	 */
	@NotBlank(message = ValidMessageConstants.APP_CREDIT_CARD_3DS_RESULT_ID_NOT_BLANK)
	private String stripeId;
	/**
	 *认证结果，0成功 1取消 2失败
	 */
	@NotNull(message = ValidMessageConstants.APP_CREDIT_CARD_3DS_RESULT_NOT_BLANK)
	private Integer result;
}
