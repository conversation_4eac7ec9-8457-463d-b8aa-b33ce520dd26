package com.platform.charger.app.entity;

import cn.hutool.core.util.ObjectUtil;
import com.platform.platform.common.core.constant.ValidMessageConstants;
import com.platform.platform.common.core.constant.enums.ResponseTypeEnum;
import com.platform.platform.common.core.util.R;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * @Created with IDEA
 * @author:sunbaolin
 * @Date:2023/6/8
 * @Time:15:31
 */
@Data
public class ChargerTestEntity {
	@NotBlank(message = ValidMessageConstants.APP_USER_NAME_NOT_BLANK)
	private String email;
	/**
	 * 用户选择充电量/金额/分钟
	 */
	private Integer chargeValue;

	/**
	 * 充电方式 0充满 1电量 2时间 3金额
	 */
	@NotNull(message = ValidMessageConstants.APP_CHARGER_TYPE_NOT_BLANK)
	private Integer chargeType;


	/**
	 * 充电桩编码
	 */
	@NotBlank(message = ValidMessageConstants.APP_CHARGER_PILE_CODE_NOT_BLANK)
	private String pileCode;

	/**
	 * 枪编码
	 */
	@NotNull(message = ValidMessageConstants.APP_CHARGER_GUN_NO_NOT_BLANK)
	private Integer gunNo;
	/**
	 * 信用卡ID
	 */
	private Integer cardId;
	private String customerId;
	private String methodId;
	/**
	 * 信用卡冻结金额
	 */
	private BigDecimal balance;
	private String currency;
	private String orderNo;
	/**
	 * 充电付费方式
	 * 0 免费
	 * 1 余额
	 * 2 信用卡
	 * 3 车队
	 * 4 卡余额
	 * 5 emsp
	 * 6 pos
	 */
	private Integer payType = 1;

	public R checkParam() {
		switch (chargeType) {
			case 1:
				if (ObjectUtil.isNull(chargeValue) || chargeValue < 1) {
					return R.ok(ResponseTypeEnum.APP_CHARGER_AMOUNT_IS_ERROR);
				}
				break;
			case 2:
				if (ObjectUtil.isNull(chargeValue) || chargeValue < 1) {
					return R.ok(ResponseTypeEnum.APP_CHARGER_TIME_IS_ERROR);
				}
				break;
			case 3:
				if (ObjectUtil.isNull(chargeValue) || chargeValue < 1) {
					return R.ok(ResponseTypeEnum.APP_CHARGER_MONEY_IS_ERROR);
				}
				break;
		}
		return R.ok();
	}
}
