package com.platform.charger.app.entity;

import com.platform.platform.common.core.constant.ValidMessageConstants;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * @Created with IDEA
 * @author:sunbaolin
 * @Date:2022/8/22
 * @Time:9:51
 */
@Data
public class ChargerUnpaidOrderEntity {
	@NotNull(message = ValidMessageConstants.APP_UNPAID_ORDER_ID_NOT_BLANK)
	private Integer orderId;
	//@NotNull(message = ValidMessageConstants.APP_UNPAID_ORDER_CREDIT_CARD_ID_NOT_BLANK)
	private Integer cardId;
	//@NotNull(message = ValidMessageConstants.APP_UNPAID_ORDER_CREDIT_CARD_ID_NOT_BLANK)
	/**
	 *支付方式
	 * 1余额
	 * 2信用卡
	 */
	private Integer payType = 2;
}
