package com.platform.charger.app.entity;

import com.platform.platform.common.core.constant.ValidMessageConstants;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * @ClassName QueryStationDetailEntity
 * <AUTHOR>
 * @DateTime 2022/6/16 17:29
 **/
@Data
public class QueryStationDetailEntity {
    /**
     * 站点Id
     */
    @NotNull(message = ValidMessageConstants.BIZ_STATION_ID_NOT_NULL)
    private Integer stationId;
    /**
     * 用户Id
     */
    private Integer userId;

}
