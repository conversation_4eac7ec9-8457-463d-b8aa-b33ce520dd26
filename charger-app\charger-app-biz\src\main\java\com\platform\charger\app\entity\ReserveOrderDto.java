package com.platform.charger.app.entity;

import com.platform.platform.common.core.constant.ValidMessageConstants;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * 预约订单参数
 */
@Data
public class ReserveOrderDto {
    /**
     *预约时间分钟
     */
    @NotNull(message = ValidMessageConstants.APP_RESERVE_TIME_NOT_BLANK)
    private Integer chargeValue;


    /**
     * 充电桩编码
     */
    @NotBlank(message = ValidMessageConstants.APP_CHARGER_PILE_CODE_NOT_BLANK)
    private String pileCode;

    /**
     * 枪编码
     */
    @NotNull(message = ValidMessageConstants.APP_CHARGER_GUN_NO_NOT_BLANK)
    private Integer gunNo;

    /**
     * 信用卡ID
     */
    private Integer cardId;

    /**
     * 信用卡冻结金额
     */
    private BigDecimal balance;
    private String orderNo;

    /**
     * 充电付费方式
     * 1 余额
     * 2 信用卡
     * 3 车队
     */
    private Integer payType = 1;

}
