package com.platform.charger.app.entity;

import com.platform.platform.common.core.constant.ValidMessageConstants;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;

@Data
public class ResetPasswordDto {

    /**
     * 邮箱验证码
     */
    @NotBlank(message = ValidMessageConstants.APP_EMAIL_CODE_NOT_NULL)
    private String verifyCode;
    /**
     * 新密码
     */
    @NotBlank(message = ValidMessageConstants.APP_NEW_PASSWORD_NOT_BLANK)
    @Length(min = 1, max = 255, message = ValidMessageConstants.APP_USER_PASSWORD_TOO_LONG + ",{min},{max}")
    private String newPassword;

    /**
     * 用户邮箱
     */
    @Pattern(regexp = "\\w+([-+.]\\w+)*@\\w+([-.]\\w+)*\\.\\w+([-.]\\w+)*",message = ValidMessageConstants.MAIL_PATTERN_ERROR)
    private String email;

    private String payUuid;
}
