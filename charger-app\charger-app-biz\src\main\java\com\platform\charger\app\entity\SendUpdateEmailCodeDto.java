package com.platform.charger.app.entity;

import com.platform.platform.common.core.constant.ValidMessageConstants;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.Email;
import javax.validation.constraints.NotBlank;

@Data
public class SendUpdateEmailCodeDto {
    /**
     * 用户新邮箱
     */
    @NotBlank(message = ValidMessageConstants.APP_USER_MAIL_NOT_BLANK)
    @Length(max = 64, message = ValidMessageConstants.APP_USER_MAIL_TOO_LONG + ",{max}")
    @Email(message = ValidMessageConstants.MAIL_PATTERN_ERROR)
    private String email;

    /**
     * 用户名
     */
//    @NotBlank(message = ValidMessageConstants.APP_USER_NAME_NOT_BLANK)
//    @Length(min = 1, max = 32, message = ValidMessageConstants.APP_USER_NAME_TOO_LONG + ",{min},{max}")
//    private String userName;

    /**
     * 密码
     */
    @NotBlank(message = ValidMessageConstants.APP_USER_PASSWORD_NOT_BLANK)
    @Length(min = 1, max = 255, message = ValidMessageConstants.APP_USER_PASSWORD_TOO_LONG + ",{min},{max}")
    private String password;

    private String ip;
}
