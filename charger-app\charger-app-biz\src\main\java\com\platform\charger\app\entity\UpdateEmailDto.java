package com.platform.charger.app.entity;

import com.platform.platform.common.core.constant.ValidMessageConstants;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.Email;
import javax.validation.constraints.NotBlank;

@Data
public class UpdateEmailDto {

    /**
     * 用户新邮箱
     */
    @NotBlank(message = ValidMessageConstants.APP_USER_MAIL_NOT_BLANK)
    @Length(max = 64, message = ValidMessageConstants.APP_USER_MAIL_TOO_LONG + ",{max}")
    @Email(message = ValidMessageConstants.MAIL_PATTERN_ERROR)
    private String email;

    /**
     * 邮箱验证码
     */
    private String verifyCode;
}
