package com.platform.charger.app.entity;

import com.platform.platform.common.core.constant.ValidMessageConstants;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;

@Data
public class UpdatePwdDto {

    /**
     * 旧密码
     */
    @NotBlank(message = ValidMessageConstants.APP_OLD_PASSWORD_NOT_BLANK)
    @Length(min = 1, max = 255, message = ValidMessageConstants.APP_USER_PASSWORD_TOO_LONG + ",{min},{max}")
    private String oldPassword;

    /**
     * 新密码
     */
    @NotBlank(message = ValidMessageConstants.APP_NEW_PASSWORD_NOT_BLANK)
    @Length(min = 1, max = 255, message = ValidMessageConstants.APP_USER_PASSWORD_TOO_LONG + ",{min},{max}")
    private String newPassword;
}
