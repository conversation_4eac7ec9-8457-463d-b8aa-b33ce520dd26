package com.platform.charger.app.entity;

import com.platform.platform.common.core.constant.ValidMessageConstants;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

@Data
public class UpdateUserInfoDto {
    @Length(max = 32, message = ValidMessageConstants.APP_USER_FIRST_NAME_TOO_LONG + ",{max}")
    private String firstName;
    @Length(max = 32, message = ValidMessageConstants.APP_USER_LAST_NAME_TOO_LONG + ",{max}")
    private String lastName;
    @Length(max = 32, message = ValidMessageConstants.APP_USER_PHONE_TOO_LONG + ",{max}")
    private String phone;
}
