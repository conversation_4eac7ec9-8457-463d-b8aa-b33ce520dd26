<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.platform.charger.app.mapper.ChargerAppCollectMapper">

    <select id="getStationIdList" resultType="java.lang.Integer">
        select station_id from t_user_station where user_id = #{userId}
    </select>

    <delete id="delStation">
        delete from t_user_station where station_id = #{stationId}
    </delete>
</mapper>