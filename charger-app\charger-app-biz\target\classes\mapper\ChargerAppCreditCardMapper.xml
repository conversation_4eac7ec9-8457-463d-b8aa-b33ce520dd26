<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.platform.charger.app.mapper.ChargerAppCreditCardMapper">
    <resultMap id="baseMapInfo" type="com.platform.charger.app.entity.ChargerAppCreditCardEntity">
        <id column="id" property="id"/>
        <result column="user_id" property="userId"/>
        <result column="card_no" property="cardNo" typeHandler="com.platform.charger.business.api.config.ChargerEncryptHandler"/>
        <result column="brand" property="brand"/>
        <result column="expire_year" property="expireYear"/>
        <result column="expire_month" property="expireMonth"/>
        <result column="cvc" property="cvc"/>
        <result column="name" property="name"/>
    </resultMap>
    <sql id="baseSqlInfo">
        id,
        user_id,
        brand,
        card_no,
        expire_year,
        expire_month,
        cvc,
        name
    </sql>

    <resultMap id="allMapInfo" type="com.platform.charger.app.entity.ChargerAppCreditCardEntity">
        <id column="id" property="id"/>
        <result column="user_id" property="userId"/>
        <result column="method_id" property="methodId"/>
        <result column="brand" property="brand"/>
        <result column="card_no" property="cardNo" typeHandler="com.platform.charger.business.api.config.ChargerEncryptHandler"/>
        <result column="expire_year" property="expireYear"/>
        <result column="expire_month" property="expireMonth"/>
        <result column="cvc" property="cvc"/>
        <result column="name" property="name"/>
    </resultMap>
    <sql id="allSqlInfo">
        id,
        user_id,
        method_id,
        brand,
        card_no,
        expire_year,
        expire_month,
        cvc,
        name
    </sql>
    <select id="info" resultMap="baseMapInfo">
        select
        <include refid="baseSqlInfo"/>
        from t_user_credit_card
        where id = #{cardId}
    </select>
    <select id="allInfo" resultMap="allMapInfo">
        select
        <include refid="allSqlInfo"/>
        from t_user_credit_card
        where id = #{cardId}
    </select>
    <select id="list" resultMap="baseMapInfo">
        select
            <include refid="baseSqlInfo"/>
        from t_user_credit_card
        where user_id = #{userId}
        order by id desc
    </select>
    <select id="allList" resultMap="allMapInfo">
        select
        <include refid="allSqlInfo"/>
        from t_user_credit_card
        where user_id = #{userId}
        order by id desc
    </select>

    <select id="getCreditCardCount" resultType="int">
        select count(1) from t_user_credit_card where user_id = #{userId}
    </select>

    <select id="getCountByCardNo" resultType="long">
        select count(1) from t_user_credit_card where user_id = #{userId}
        and card_no = #{cardNo,typeHandler=com.platform.charger.business.api.config.ChargerEncryptHandler}
    </select>
</mapper>