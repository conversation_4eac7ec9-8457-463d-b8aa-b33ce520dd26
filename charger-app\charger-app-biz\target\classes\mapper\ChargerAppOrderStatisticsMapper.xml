<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.platform.charger.app.mapper.ChargerAppOrderStatisticsMapper">
    <resultMap id="appMap" type="com.platform.charger.app.entity.AppOrderStatisticsVo">
        <result column="pay_time" property="time"/>
        <result column="count" property="count"/>
        <result column="actual_fee" property="actualFee"/>
        <result column="elec" property="elec"/>
        <result column="duration" property="duration"/>
    </resultMap>

    <sql id="appSql">
        pay_time,
        sum(count) as count,
        sum(actual_fee) as actual_fee,
        sum(elec) as elec,
        TRUNCATE(sum(duration) / 3600, 2) as duration
    </sql>

    <select id="getAppChargerOrderStatistics" resultMap="appMap">
        select
        <include refid="appSql"/>
        from t_charger_order_statistics
        where app_id = #{query.userId} and pay_time between #{query.start} and #{query.end} group by pay_time order by pay_time
    </select>

    <select id="getAppUserTradeCount" resultType="com.platform.charger.app.entity.AppUserDigitalStatisticEntity">
        select
        count,
        mCount,
        yCount,
        lastYCount
        from
        (select
        count(distinct (case when pay_time = #{query.time} then app_id end)) as count,
        count(distinct (case when pay_time = #{query.mTime} then app_id end)) as mCount
        from t_charger_order_statistics
        <where>
            <if test="query.stationId != null">
                and station_id = #{query.stationId}
            </if>
            <if test="query.agentIds != null and query.agentIds.size() != 0">
                and agent_id in
                <foreach collection="query.agentIds" item="agentId" index="index" open="(" separator="," close=")">
                    #{agentId}
                </foreach>
            </if>
            and pay_time between #{query.mTime} and #{query.time}
        </where>) t1
        join
        (select
        count(distinct app_id) as yCount
        from t_charger_order_statistics
        <where>
            <if test="query.stationId != null">
                and station_id = #{query.stationId}
            </if>
            <if test="query.agentIds != null and query.agentIds.size() != 0">
                and agent_id in
                <foreach collection="query.agentIds" item="agentId" index="index" open="(" separator="," close=")">
                    #{agentId}
                </foreach>
            </if>
            and pay_time between #{query.ySTime} and #{query.yETime}
        </where>) t2
        join
        (select
        count(distinct app_id) as lastYCount
        from t_charger_order_statistics
        <where>
            <if test="query.stationId != null">
                and station_id = #{query.stationId}
            </if>
            <if test="query.agentIds != null and query.agentIds.size() != 0">
                and agent_id in
                <foreach collection="query.agentIds" item="agentId" index="index" open="(" separator="," close=")">
                    #{agentId}
                </foreach>
            </if>
            and pay_time between #{query.lastYSTime} and #{query.lastYETime}
        </where>) t3
    </select>

    <select id="getFirstTimeUserCount" resultType="com.platform.charger.app.entity.AppUserDigitalStatisticEntity">
        select
        count,
        mCount,
        yCount,
        lastYCount
        from
        (select
        count(distinct (case when pay_time = #{query.time} then app_id end)) as count,
        count(distinct (case when pay_time = #{query.mTime} then app_id end)) as mCount
        from t_charger_order_statistics
        <where>
            <if test="query.stationId != null">
                and station_id = #{query.stationId}
            </if>
            <if test="query.agentIds != null and query.agentIds.size() != 0">
                and agent_id in
                <foreach collection="query.agentIds" item="agentId" index="index" open="(" separator="," close=")">
                    #{agentId}
                </foreach>
            </if>
            and pay_time between #{query.mTime} and #{query.time}
            and first_charge_flag = 1
        </where>) t1
        join
        (select
        count(distinct app_id) as yCount
        from t_charger_order_statistics
        <where>
            <if test="query.stationId != null">
                and station_id = #{query.stationId}
            </if>
            <if test="query.agentIds != null and query.agentIds.size() != 0">
                and agent_id in
                <foreach collection="query.agentIds" item="agentId" index="index" open="(" separator="," close=")">
                    #{agentId}
                </foreach>
            </if>
            and pay_time between #{query.ySTime} and #{query.yETime}
            and first_charge_flag = 1
        </where>) t2
        join
        (select
        count(distinct app_id) as lastYCount
        from t_charger_order_statistics
        <where>
            <if test="query.stationId != null">
                and station_id = #{query.stationId}
            </if>
            <if test="query.agentIds != null and query.agentIds.size() != 0">
                and agent_id in
                <foreach collection="query.agentIds" item="agentId" index="index" open="(" separator="," close=")">
                    #{agentId}
                </foreach>
            </if>
            and pay_time between #{query.lastYSTime} and #{query.lastYETime}
            and first_charge_flag = 1
        </where>) t3
    </select>

    <select id="getChargeUserList" resultType="int">
        select distinct app_id from t_charger_order_statistics where pay_time between #{startTime} and #{endTime}
        <if test="stationId != null">
            and station_id = #{stationId}
        </if>
        <if test="agentIds != null and agentIds.size() != 0">
            and agent_id in
            <foreach collection="agentIds" item="agentId" index="index" open="(" separator="," close=")">
                #{agentId}
            </foreach>
        </if>
    </select>

    <select id="getFirstTimeUserColumnData" resultType="com.platform.charger.app.entity.AppUserColumnStatisticEntity">
        select
        time as time,
        yTime as yTime,
        count as count,
        yCount as yCount
        from (
        select
        pay_time as time,
        0 as yTime,
        count(app_id) as count,
        0 as yCount
        from t_charger_order_statistics where first_charge_flag = 1 and pay_time between #{query.ySTime} and #{query.yETime}
        <if test="query.stationId != null">
            and station_id = #{query.stationId}
        </if>
        <if test="query.agentIds != null and query.agentIds.size() != 0">
            and agent_id in
            <foreach collection="query.agentIds" item="agentId" index="index" open="(" separator="," close=")">
                #{agentId}
            </foreach>
        </if>
        group by pay_time
        union all
        select
        0 as time,
        pay_time as yTime,
        0 as count,
        count(app_id) as yCount
        from t_charger_order_statistics where first_charge_flag = 1 and pay_time = #{query.lastYSTime} and #{query.lastYETime}
        <if test="query.stationId != null">
            and station_id = #{query.stationId}
        </if>
        <if test="query.agentIds != null and query.agentIds.size() != 0">
            and agent_id in
            <foreach collection="query.agentIds" item="agentId" index="index" open="(" separator="," close=")">
                #{agentId}
            </foreach>
        </if>
        group by pay_time
        ) t order by t.time,t.yTime
    </select>

    <select id="getActiveUserColumnData" resultType="com.platform.charger.app.entity.AppUserColumnStatisticEntity">
        select
        time as time,
        yTime as yTime,
        count as count,
        yCount as yCount
        from (
        select
        pay_time as time,
        0 as yTime,
        count(distinct app_id) as count,
        0 as yCount
        from t_charger_order_statistics where pay_time between #{query.ySTime} and #{query.yETime}
        <if test="query.stationId != null">
            and station_id = #{query.stationId}
        </if>
        <if test="query.agentIds != null and query.agentIds.size() != 0">
            and agent_id in
            <foreach collection="query.agentIds" item="agentId" index="index" open="(" separator="," close=")">
                #{agentId}
            </foreach>
        </if>
        group by pay_time
        union all
        select
        0 as time,
        pay_time as yTime,
        0 as count,
        count(distinct app_id) as yCount
        from t_charger_order_statistics where pay_time = #{query.lastYSTime} and #{query.lastYETime}
        <if test="query.stationId != null">
            and station_id = #{query.stationId}
        </if>
        <if test="query.agentIds != null and query.agentIds.size() != 0">
            and agent_id in
            <foreach collection="query.agentIds" item="agentId" index="index" open="(" separator="," close=")">
                #{agentId}
            </foreach>
        </if>
        group by pay_time
        ) t order by t.time,t.yTime
    </select>
</mapper>