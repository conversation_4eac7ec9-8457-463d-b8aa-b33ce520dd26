<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.platform.charger.app.mapper.ChargerAppPayStatusMapper">
    <resultMap id="baseMapInfo" type="com.platform.charger.app.entity.ChargerAppPayStatusEntity">
        <id column="id" property="id"/>
        <result column="user_id" property="userId"/>
        <result column="payment_id" property="paymentId"/>
        <result column="status" property="status"/>
        <result column="order_type" property="orderType"/>
        <result column="order_no" property="orderNo"/>
        <result column="confirm_amount" property="confirmAmount"/>
        <result column="capture_amount" property="captureAmount"/>
        <result column="currency" property="currency"/>
        <result column="pay_uuid" property="payUuid"/>
        <result column="remark" property="remark"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
    </resultMap>
    <sql id="baseSqlInfo">
        id,
        user_id,
        payment_id,
        status,
        order_type,
        order_no,
        confirm_amount,
        capture_amount,
        currency,
        pay_uuid,
        remark,
        create_time,
        update_time
    </sql>
    <select id="info" resultMap="baseMapInfo">
        select
        <include refid="baseSqlInfo"/>
        from t_user_pay_status
        where id = #{id}
    </select>
    <select id="getInfo" resultMap="baseMapInfo">
        select
            <include refid="baseSqlInfo"/>
        from t_user_pay_status
        <where>
            <if test="query.orderNo != null and query.orderNo != ''">
                and order_no =  #{query.orderNo}
            </if>
            <if test="query.paymentId != null and query.paymentId != ''">
                 and payment_id =  #{query.paymentId}
            </if>
            <if test="query.userId != null">
                and user_id =  #{query.userId}
            </if>
        </where>
        limit 1
    </select>

    <select id="list" resultMap="baseMapInfo">
        select
        <include refid="baseSqlInfo"/>
        from t_user_pay_status
        <where>
            <if test="query.orderNo != null and query.orderNo != ''">
                and order_no =  #{query.orderNo}
            </if>
            <if test="query.paymentId != null and query.paymentId != ''">
                and payment_id =  #{query.paymentId}
            </if>
            <if test="query.userId != null">
                and user_id =  #{query.userId}
            </if>
            <if test="query.orderType != null">
                and order_type =  #{query.orderType}
            </if>
            <if test="query.refundFlag != null">
                and refund_flag =  #{query.refundFlag}
            </if>
            <if test="query.status != null and query.status != ''">
                and status =  #{query.status}
            </if>
        </where>
        order by id desc
    </select>
</mapper>