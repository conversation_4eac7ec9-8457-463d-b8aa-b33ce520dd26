<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.platform.charger.app.mapper.ChargerAppUserCardMapper">
    <resultMap id="baseMap" type="com.platform.charger.app.api.entity.TUserCard">
        <result property="id" column="id"/>
        <result property="userId" column="user_id"/>
        <result property="cardId" column="card_id"/>
        <result property="cardSn" column="card_sn"/>
        <result property="status" column="status"/>
    </resultMap>

    <resultMap id="userCardListMap" type="com.platform.charger.app.entity.AppUserCardVo">
        <result property="userId" column="user_id"/>
        <result property="userName" column="user_name"/>
        <collection property="cards" ofType="com.platform.charger.app.entity.AppUserCardInfo">
            <result property="cardId" column="card_id"/>
            <result property="cardSn" column="card_sn"/>
            <result property="status" column="status"/>
            <result property="balance" column="balance"/>
            <result property="type" column="type"/>
        </collection>
    </resultMap>

    <sql id="baseSql">
        id,
        user_id,
        card_id,
        card_sn,
        status
    </sql>

    <select id="getCardList" resultMap="userCardListMap">
        select
        t1.id as user_id,
        t1.user_name as user_name,
        t2.card_id as card_id,
        t2.card_sn as card_sn,
        t2.type as type,
        t2.status as status,
        t2.balance as balance
        from t_user as t1 , t_user_card as t2
        <where>
            <if test="query != null">
                t1.id = #{query}
                and t1.id = t2.user_id
            </if>
        </where>
    </select>
</mapper>