<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.platform.charger.app.mapper.ChargerAppUserMapper">
    <resultMap id="baseMap" type="com.platform.charger.app.entity.AppUserInfoVo">
        <id column="id" property="userId"/>
        <result column="short_id" property="shortId"/>
        <result column="phone" property="phone" typeHandler="com.platform.charger.business.api.config.ChargerEncryptHandler"/>
        <result column="email" property="email" typeHandler="com.platform.charger.business.api.config.ChargerEncryptHandler"/>
        <result column="user_level" property="userLevel"/>
        <result column="account_balance" property="accountBalance"/>
        <result column="last_login_time" property="lastLoginTime"/>
        <result column="last_login_source" property="lastLoginSource"/>
        <result column="create_time" property="createTime"/>
        <result column="first_name" property="firstName" typeHandler="com.platform.charger.business.api.config.ChargerEncryptHandler"/>
        <result column="last_name" property="lastName" typeHandler="com.platform.charger.business.api.config.ChargerEncryptHandler"/>
        <result column="currency" property="currency"/>
    </resultMap>

    <resultMap id="webMap" type="com.platform.charger.app.api.entity.TUser">
        <id column="id" property="id"/>
        <result column="user_name" property="userName" typeHandler="com.platform.charger.business.api.config.ChargerEncryptHandler"/>
        <result column="short_id" property="shortId"/>
        <result column="phone" property="phone" typeHandler="com.platform.charger.business.api.config.ChargerEncryptHandler"/>
        <result column="email" property="email" typeHandler="com.platform.charger.business.api.config.ChargerEncryptHandler"/>
        <result column="is_home_pile" property="isHomePile"/>
        <result column="password" property="password"/>
        <result column="pay_uuid" property="payUuid"/>
        <result column="reg_source" property="regSource"/>
        <result column="user_level" property="userLevel"/>
        <result column="account_balance" property="accountBalance"/>
        <result column="frozen_amount" property="frozenAmount"/>
        <result column="lock_flag" property="lockFlag"/>
        <result column="lock_time" property="lockTime"/>
        <result column="create_time" property="createTime"/>
        <result column="last_login_time" property="lastLoginTime"/>
        <result column="last_login_source" property="lastLoginSource"/>
        <result column="last_ip" property="lastIp"/>
        <result column="first_name" property="firstName" typeHandler="com.platform.charger.business.api.config.ChargerEncryptHandler"/>
        <result column="last_name" property="lastName" typeHandler="com.platform.charger.business.api.config.ChargerEncryptHandler"/>
        <result column="app_version" property="appVersion"/>
    </resultMap>

    <resultMap id="appMap" type="com.platform.charger.app.api.entity.TUser">
        <id column="id" property="id"/>
        <result column="email" property="email" typeHandler="com.platform.charger.business.api.config.ChargerEncryptHandler"/>
    </resultMap>

    <sql id="baseSql">
        id,
        short_id,
        phone,
        email,
        user_level,
        account_balance,
        last_login_time,
        last_login_source,
        create_time,
        first_name,
        last_name,
        currency
    </sql>

    <sql id="webSql">
        id,
        user_name,
        password,
        short_id,
        phone,
        email,
        is_home_pile,
        reg_source,
        user_level,
        account_balance,
        frozen_amount,
        pay_uuid,
        lock_flag,
        lock_time,
        create_time,
        last_login_time,
        last_login_source,
        last_ip,
        first_name,
        last_name,
        app_version
    </sql>

    <sql id="webListSql">
        t1.id,
        t1.user_name,
        t1.short_id,
        t1.phone,
        t1.email,
        t1.is_home_pile,
        t1.reg_source,
        t1.user_level,
        t1.account_balance,
        t1.frozen_amount,
        t1.lock_flag,
        t1.lock_time,
        t1.create_time,
        t1.last_login_time,
        t1.last_login_source,
        t1.last_ip,
        t1.first_name,
        t1.last_name,
        t1.app_version
    </sql>

    <sql id="appSql">
        id,
        email
    </sql>

    <select id="getUserInfo" resultMap="baseMap">
        select
        <include refid="baseSql"/>
        from t_user where id = #{id} and del_flag = 0
    </select>

    <select id="getUserInfoById" resultMap="baseMap">
        select
        <include refid="baseSql"/>
        from t_user where id = #{id}
    </select>

    <select id="getAppUserList" resultMap="webMap">
        select distinct
        <include refid="webListSql"/>
        from t_user t1 left join t_user_card t2 on t2.user_id = t1.id
        <where>
            <if test="query.userName != null and query.userName != ''">
                and t1.email = #{query.userName,typeHandler=com.platform.charger.business.api.config.ChargerEncryptHandler}
            </if>
            <if test="query.payUuid != null and query.payUuid != ''">
                and pay_uuid = #{query.payUuid}
            </if>
            <if test="query.lastLoginSource != null">
                and t1.last_login_source = #{query.lastLoginSource}
            </if>
            <if test="query.userLevel != null">
                and t1.user_level = #{query.userLevel}
            </if>
            <if test="query.lockFlag != null">
                and t1.lock_flag = #{query.lockFlag}
            </if>
            <if test="query.cardSn != null and query.cardSn != ''">
                and t2.card_sn like concat(#{query.cardSn}, '%')
            </if>
            and t1.del_flag = 0
        </where>
        order by t1.id desc
    </select>

    <select id="getAppUserInfo" resultMap="webMap">
        select
        <include refid="webSql"/>
        from t_user
        <where>
            <if test="query.email != null and query.email != ''">
                and email = #{query.email,typeHandler=com.platform.charger.business.api.config.ChargerEncryptHandler}
            </if>
            <if test="query.payUuid != null and query.payUuid != ''">
                and pay_uuid = #{query.payUuid}
            </if>
            <if test="query.shortId != null and query.shortId != ''">
                and short_id = #{query.shortId}
            </if>
            <if test="query.lastLoginSource != null">
                and last_login_source = #{query.lastLoginSource}
            </if>
            <if test="query.userLevel != null">
                and user_level = #{query.userLevel}
            </if>
            <if test="query.lockFlag != null">
                and lock_flag = #{query.lockFlag}
            </if>
            and del_flag = 0
        </where>
        limit 1
    </select>

    <update id="addFrozenAmount">
        UPDATE t_user
        SET account_balance = account_balance - #{user.account_balance},
            frozen_amount = frozen_amount + #{user.account_balance},
            update_time = now()
        WHERE id = #{user.id}
          AND  account_balance >= #{user.account_balance}
    </update>

    <update id="returnFrozenAmount">
        UPDATE t_user
        SET account_balance = account_balance + #{user.account_balance},
            frozen_amount = frozen_amount - #{user.account_balance},
            update_time = now()
        WHERE id = #{user.id}
    </update>

    <select id="updateAccountBalanceById" resultType="java.math.BigDecimal">
        begin;
        select account_balance from t_user where id = #{userId} for update;
        update t_user set account_balance = account_balance - #{deductionAmount} where id = #{userId};
        commit;
    </select>

    <select id="getAccountBalanceByUserId" resultType="decimal">
        select account_balance from t_user where id = #{userId} and del_flag = 0 and lock_flag = 0
    </select>
    
    <select id="getCurrentRegisterCount" resultType="int">
        select count(1) from t_user where del_flag = 0 and lock_flag = 0 and create_time between #{query.startTime} and #{query.endTime}
        <if test="query.payUuid != null and query.payUuid != ''">
            and pay_uuid = #{query.payUuid}
        </if>
    </select>

    <select id="getBeforeRegisterCount" resultType="int">
        select count(1) from t_user where del_flag = 0 and create_time &lt;= #{query.endTime}
        <if test="query.payUuid != null and query.payUuid != ''">
            and pay_uuid = #{query.payUuid}
        </if>
    </select>

    <select id="getActiveCount" resultType="int">
        select count(1) from t_user where del_flag = 0 and lock_flag = 0 and last_login_time between #{query.startTime} and #{query.endTime}
        <if test="query.payUuid != null and query.payUuid != ''">
            and pay_uuid = #{query.payUuid}
        </if>
    </select>

<!--    <select id="getUserInfoByName" resultMap="appMap">-->
<!--        select-->
<!--        <include refid="appSql"/>-->
<!--        from t_user where del_flag = 0 and lock_flag = 0 and user_name = #{userName}-->
<!--    </select>-->

<!--    <select id="getUserInfoByEmail" resultMap="appMap">-->
<!--        select-->
<!--        <include refid="appSql"/>-->
<!--        from t_user where del_flag = 0 and lock_flag = 0 and email = #{email,typeHandler=com.platform.charger.business.api.config.ChargerEncryptHandler}-->
<!--        <if test="uuid != null and uuid != ''">-->
<!--            and pay_uuid = #{uuid}-->
<!--        </if>-->
<!--        limit 1-->
<!--    </select>-->

    <update id="updateUserHomePileFlag">
        update t_user set is_home_pile = 0 where id = #{userId}
    </update>

    <update id="revertUserHomePileFlag">
        update t_user set is_home_pile = 1 where id = #{userId}
    </update>

    <select id="getAppUserCount" resultType="com.platform.charger.app.entity.AppUserDigitalStatisticEntity">
        select
        count,
        mCount,
        lastYCount
        from
        (select
        count(1) as count
        from t_user where del_flag = 0 and create_time &lt;= #{query.endTime}
        <if test="query.payUuid != null and query.payUuid != ''">
            and pay_uuid = #{query.payUuid}
        </if>) t1
        join
        (select
        count(1) as mCount
        from t_user where del_flag = 0 and create_time &lt;= #{query.mEndTime}
        <if test="query.payUuid != null and query.payUuid != ''">
            and pay_uuid = #{query.payUuid}
        </if>) t2
        join
        (select
        count(1) as lastYCount
        from t_user where del_flag = 0 and create_time &lt;= #{query.yLastEndTime}
        <if test="query.payUuid != null and query.payUuid != ''">
            and pay_uuid = #{query.payUuid}
        </if>) t3
    </select>

    <select id="getAppUserTypeVo" resultType="com.platform.charger.app.entity.AppUserTypeVo">
        select
        count(1) as totalUserCount,
        sum(case when reg_source = 1 then 1 else 0 end) as appleUserCount,
        sum(case when reg_source = 0 then 1 else 0 end) as androidUserCount,
        sum(case when reg_source = 2 then 1 else 0 end) as otherUserCount
        from t_user where del_flag = 0 and create_time &lt;= #{query.endTime}
        <if test="query.payUuid != null and query.payUuid != ''">
            and pay_uuid = #{query.payUuid}
        </if>
    </select>

    <update id="updateEmailByUserId">
        update t_user set email = #{email,typeHandler=com.platform.charger.business.api.config.ChargerEncryptHandler},
               update_time = now() where id = #{userId}
    </update>
</mapper>