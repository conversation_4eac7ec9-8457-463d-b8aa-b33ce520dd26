<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.platform.charger.app.mapper.ChargerAppVehicleMapper">
    <resultMap id="baseMap" type="com.platform.charger.app.api.entity.TUserVehicle">
        <id column="id" property="id"/>
        <result column="user_id" property="userId"/>
        <result column="vehicle_no" property="vehicleNo"/>
        <result column="vehicle_brand" property="vehicleBrand"/>
        <result column="vehicle_charger_type" property="vehicleChargerType"/>
        <result column="vehicle_max_power" property="vehicleMaxPower"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
    </resultMap>

    <sql id="baseSql">
        id,
        user_id,
        vehicle_no,
        vehicle_brand,
        vehicle_charger_type,
        vehicle_max_power,
        create_time,
        update_time
    </sql>

    <select id="getVehicleList" resultMap="baseMap">
        select
        <include refid="baseSql"/>
        from t_user_vehicle where user_id = #{query.userId} and del_flag = 0 order by id desc
    </select>
</mapper>