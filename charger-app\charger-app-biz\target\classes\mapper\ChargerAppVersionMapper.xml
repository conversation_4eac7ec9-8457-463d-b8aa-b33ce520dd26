<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.platform.charger.app.mapper.ChargerAppVersionMapper">
    <resultMap id="baseMap" type="com.platform.charger.app.entity.AppVersionEntity">
        <id column="id" property="id"/>
        <result column="name" property="name"/>
        <result column="code" property="code"/>
        <result column="type" property="type"/>
        <result column="force_flag" property="forceFlag"/>
        <result column="agent_id" property="agentId"/>
        <result column="agent_name" property="agentName"/>
        <result column="remark" property="remark"/>
        <result column="create_by" property="createBy"/>
        <result column="create_time" property="createTime"/>
        <result column="update_by" property="updateBy"/>
        <result column="update_time" property="updateTime"/>
    </resultMap>

    <sql id="baseSql">
        id,
        name,
        code,
        type,
        force_flag,
        agent_id,
        agent_name,
        remark,
        create_by,
        create_time,
        update_by,
        update_time
    </sql>

    <sql id="appSql">
         id,
        name,
        code,
        force_flag,
        remark
    </sql>

    <select id="list" resultMap="baseMap">
        select
        <include refid="baseSql"/>
        from t_app_version
        <where>
            <if test="query.name != null and query.name != ''">
                and name like concat(#{query.name}, '%')
            </if>
            <if test="query.type != null">
                and type = #{query.type}
            </if>
            <if test="query.agentIds != null and query.agentIds.size() != 0">
                and agent_id in
                <foreach item="item" index="index" collection="query.agentIds" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            and del_flag = 0
        </where>
        order by id desc
    </select>

    <select id="getAppVersion" resultMap="baseMap">
        select
        <include refid="appSql"/>
        from t_app_version where agent_id = #{agentId} and type = #{type} and del_flag = 0
    </select>
</mapper>