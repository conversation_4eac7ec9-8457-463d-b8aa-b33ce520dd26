<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.platform.charger.app.mapper.ChargerOpsUserMapper">
    <resultMap id="baseMap" type="com.platform.charger.app.api.entity.TOpsUser">
        <id column="id" property="id"/>
        <result column="user_name" property="userName"/>
        <result column="phone" property="phone"/>
        <result column="expire_time" property="expireTime"/>
        <result column="last_login_time" property="lastLoginTime"/>
        <result column="last_login_source" property="lastLoginSource"/>
        <result column="last_ip" property="lastIp"/>
<!--        <result column="agent_name" property="agentName"/>-->
        <result column="lock_flag" property="lockFlag"/>
        <result column="lock_time" property="lockTime"/>
        <result column="create_by" property="createBy"/>
        <result column="create_time" property="createTime"/>
        <result column="update_by" property="updateBy"/>
        <result column="update_time" property="updateTime"/>
    </resultMap>

    <sql id="baseSql">
        id,
        user_name,
        phone,
        DATE_FORMAT(expire_time,'%Y-%m-%d') as expire_time,
        last_login_time,
        last_login_source,
        agent_name,
        lock_flag,
        lock_time,
        create_by,
        create_time,
        update_by,
        update_time,
        last_ip
    </sql>

    <select id="getOpsUserList" resultMap="baseMap">
        select
        <include refid="baseSql"/>
        from t_ops_user
        <where>
            <if test="query.userName != null and query.userName != ''">
                and user_name like concat(#{query.userName}, '%')
            </if>
            <if test="query.agentIds != null and query.agentIds.size() != 0">
                and agent_id in
                <foreach item="item" index="index" collection="query.agentIds" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="query.lockFlag != null">
                and lock_flag = #{query.lockFlag}
            </if>
            and del_flag = 0
        </where>
        order by id desc
    </select>
</mapper>