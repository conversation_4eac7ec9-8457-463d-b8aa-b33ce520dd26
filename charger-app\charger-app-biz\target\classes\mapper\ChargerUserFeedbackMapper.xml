<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.platform.charger.app.mapper.ChargerUserFeedbackMapper">
    <resultMap id="appMap" type="com.platform.charger.app.api.entity.ChargerUserFeedbackEntity">
        <result column="create_time" property="createTime"/>
        <result column="message" property="message"/>
        <collection property="imgList" ofType="com.platform.charger.app.api.entity.ChargerUserFeedbackImgEntity">
            <result column="url" property="url"/>
        </collection>
    </resultMap>

    <resultMap id="webListMap" type="com.platform.charger.app.api.entity.ChargerUserFeedbackEntity">
        <id column="id" property="id"/>
        <result column="create_time" property="createTime"/>
        <result column="email" property="email" typeHandler="com.platform.charger.business.api.config.ChargerEncryptHandler"/>
        <result column="user_id" property="userId"/>
        <result column="agent_name" property="agentName"/>
    </resultMap>

    <resultMap id="webInfoMap" type="com.platform.charger.app.api.entity.ChargerUserFeedbackEntity">
        <id column="id" property="id"/>
        <result column="message" property="message"/>
        <collection property="imgList" ofType="com.platform.charger.app.api.entity.ChargerUserFeedbackImgEntity">
            <result column="url" property="url"/>
        </collection>
    </resultMap>

    <sql id="appSql">
        t1.create_time,
        t1.message,
        t2.url
    </sql>

    <sql id="webListSql">
       id,
       user_id,
       email,
       agent_name,
       create_time
    </sql>

    <sql id="webInfoSql">
        t1.id,
        t1.message,
        t2.url
    </sql>

    <select id="getFeedBackContentList" resultMap="appMap">
        select
        <include refid="appSql"/>
        from t_user_feedback t1 left join t_user_feedback_img t2 on t1.id = t2.feedback_id
        where t1.user_id = #{userId}
    </select>

    <select id="getFeedBackList" resultMap="webListMap">
        select
        <include refid="webListSql"/>
        from t_user_feedback
        <where>
            <if test="query.agentIds != null and query.agentIds.size > 0">
                and agent_id in
                <foreach collection="query.agentIds" index="index" item="agentId" open="(" separator="," close=")">
                    #{agentId}
                </foreach>
            </if>
            <if test="query.startTime != null and query.startTime != ''">
                and create_time &gt;= #{query.startTime}
            </if>
            <if test="query.endTime != null and query.endTime != ''">
                and create_time &lt;= #{query.endTime}
            </if>
            <if test="query.appEmail != null and query.appEmail != ''">
                and email = #{query.appEmail, typeHandler=com.platform.charger.business.api.config.ChargerEncryptHandler}
            </if>
        </where>
        order by id desc
    </select>

    <select id="getFeedBackInfo" resultMap="webInfoMap">
        select
        <include refid="webInfoSql"/>
        from t_user_feedback t1 left join t_user_feedback_img t2 on t1.id = t2.feedback_id
        where t1.id = #{id}
    </select>
</mapper>