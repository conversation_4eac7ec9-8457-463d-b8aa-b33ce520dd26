<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.platform.platform.admin.mapper.PlatformLogMapper">

    <resultMap id="sysLogMap" type="com.platform.platform.admin.api.entity.PlatformLogEntity">
        <id property="id" column="id"/>
        <result property="type" column="type"/>
        <result property="title" column="title"/>
        <result property="serviceId" column="service_id"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="remoteAddr" column="remote_addr"/>
        <result property="userAgent" column="user_agent"/>
        <result property="requestUri" column="request_uri"/>
        <result property="method" column="method"/>
        <result property="time" column="time"/>
        <result property="delFlag" column="del_flag"/>
        <result property="exception" column="exception"/>
    </resultMap>

    <sql id="sysLogSql">
        id,
        type,
        title,
        service_id,
        create_by,
        create_time,
        update_time,
        remote_addr,
        user_agent,
        request_uri,
        method,
        time,
        del_flag,
        exception
    </sql>

    <select id="getLogPage" resultMap="sysLogMap">
        select
        <include refid="sysLogSql"/>
        from sys_log
        <where>
            <if test="query.title != null and query.title != ''">
                and title like concat('%', #{query.title}, '%')
            </if>
            <if test="query.createBy != null and query.createBy != ''">
                and create_by = #{query.createBy}
            </if>
            <if test="query.startTime != null and query.startTime != ''">
                and create_time &gt;= #{query.startTime}
            </if>
            <if test="query.endTime != null and query.endTime !=''">
                and create_time &lt;= #{query.endTime}
            </if>
            <if test="query.userNameList != null and query.userNameList.size() > 0">
                and create_by in
                <foreach collection="query.userNameList" item="userName" index="index" open="(" separator="," close=")">
                    #{userName}
                </foreach>
            </if>
            and del_flag = 0
        </where>
        order by create_time desc
    </select>

    <select id="getLogInfo" resultMap="sysLogMap">
        select
        <include refid="sysLogSql"/>
        from sys_log where id = #{id} and del_flag = 0
    </select>
</mapper>
