<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.platform.platform.admin.mapper.PlatformMailMapper">
    <resultMap id="baseMapInfo" type="com.platform.platform.admin.api.entity.PlatformMailEntity">
        <id column="id" property="id"/>
        <result column="host" property="host"/>
        <result column="port" property="port"/>
        <result column="user_name" property="userName"/>
        <result column="password" property="password"/>
        <result column="create_by" property="createBy"/>
        <result column="create_time" property="createTime"/>
        <result column="update_by" property="updateBy"/>
        <result column="update_time" property="updateTime"/>
    </resultMap>

    <sql id="baseSqlInfo">
        id,
        host,
        port,
        user_name,
        password,
        create_by,
        create_time,
        update_by,
        update_time
    </sql>
    <select id="info" resultMap="baseMapInfo">
        select
            <include refid="baseSqlInfo"/>
        from sys_email
        where id = #{id}
    </select>
    <select id="list" resultMap="baseMapInfo">
        select
            <include refid="baseSqlInfo"/>
        from sys_email
        <where>
            <if test="query.name != null and query.name != ''">
                and user_name like CONCAT('%',#{query.name},'%')
            </if>
        </where>
        order by id desc
    </select>

    <select id="listByOperator" resultMap="baseMapInfo">
        SELECT
           id,
           HOST,
           PORT,
           user_name,
           PASSWORD
        FROM
            sys_email
        <where>
            <if test="query.name != null and query.name != ''">
                and user_name like CONCAT('%',#{query.name},'%')
            </if>
            <if test="query.createName != null and query.createName.size() != 0">
                and create_by in
                <foreach item="item" index="index" collection="query.createName" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </where>
        UNION
        SELECT
            t1.id,
            t1.HOST,
            t1.PORT,
            t1.user_name,
            t1.PASSWORD
        FROM
            sys_email t1
        LEFT JOIN t_operator t2 ON t1.id = t2.mail_id
        <where>
            and t2.del_flag = 0
            <if test="query.name != null and query.name != ''">
                and t1.user_name like CONCAT('%',#{query.name},'%')
            </if>
            <if test="query.createName != null and query.createName.size() != 0">
                and t1.create_by in
                <foreach item="item" index="index" collection="query.createName" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </where>
        order by id desc
    </select>
    <select id="getInfoByUuid" resultMap="baseMapInfo">
        select
            t1.id,
            t1.host,
            t1.port,
            t1.user_name,
            t1.password
        from sys_email t1
        LEFT JOIN t_operator t2 ON t1.id = t2.mail_id
        where t2.uuid = #{uuid}
        and t2.del_flag = 0
        limit 1
    </select>
    <select id="getInfoByOperatorId" resultMap="baseMapInfo">
        select
            t1.id,
            t1.host,
            t1.port,
            t1.user_name,
            t1.password
        from sys_email t1
        LEFT JOIN t_operator t2 ON t1.id = t2.mail_id
        where t2.id = #{agentId}
        and t2.del_flag = 0
        limit 1
    </select>
</mapper>