<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.platform.platform.admin.mapper.PlatformMenuMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.platform.platform.admin.api.entity.PlatformMenuEntity">
        <id column="menu_id" property="menuId"/>
        <result column="name" property="name"/>
        <result column="permission" property="permission"/>
        <result column="path" property="path"/>
        <result column="parent_id" property="parentId"/>
        <result column="icon" property="icon"/>
        <result column="component" property="component"/>
        <result column="sort" property="sort"/>
        <result column="type" property="type"/>
        <result column="keep_alive" property="keepAlive"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="del_flag" property="delFlag"/>
    </resultMap>

    <resultMap id="MenuVoResultMap" type="com.platform.platform.admin.api.vo.MenuVO">
        <id column="menu_id" property="menuId"/>
        <result column="name" property="name"/>
        <result column="permission" property="permission"/>
        <result column="parent_id" property="parentId"/>
        <result column="icon" property="icon"/>
        <result column="path" property="path"/>
        <result column="component" property="component"/>
        <result column="sort" property="sort"/>
        <result column="keep_alive" property="keepAlive"/>
        <result column="type" property="type"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="del_flag" property="delFlag"/>
    </resultMap>

    <!--通过角色查询菜单信息-->
    <select id="listMenusByRoleId" resultMap="MenuVoResultMap">
        SELECT sys_menu.*
        FROM sys_menu
                 LEFT JOIN sys_role_menu ON sys_menu.menu_id = sys_role_menu.menu_id
        WHERE sys_menu.del_flag = 0
          AND sys_role_menu.role_id = #{roleId}
        ORDER BY sys_menu.sort DESC
    </select>

    <!--通过角色ID 查询权限-->
    <select id="listPermissionsByRoleIds" resultType="java.lang.String">
        SELECT m.permission
        FROM sys_menu m,
             sys_role_menu rm
        WHERE m.menu_id = rm.menu_id
          AND m.del_flag = 0
          AND rm.role_id IN (#{roleIds})
    </select>

    <select id="getMenuIdByRoleId" resultType="java.lang.Integer">
        select menu_id
        from sys_role_menu
        where role_id = #{roleId}
    </select>

    <select id="listMenusByAdmin" resultMap="MenuVoResultMap">
        SELECT *
        FROM sys_menu
        WHERE del_flag = 0
        ORDER BY sort DESC
    </select>
</mapper>
