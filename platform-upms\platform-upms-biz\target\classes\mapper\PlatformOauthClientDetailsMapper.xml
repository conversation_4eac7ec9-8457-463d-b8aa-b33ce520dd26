<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.platform.platform.admin.mapper.PlatformOauthClientDetailsMapper">

    <resultMap id="sysOauthClientDetailsMap"
               type="com.platform.platform.admin.api.entity.PlatformOauthClientDetailsEntity">
        <id property="clientId" column="client_id"/>
        <result property="resourceIds" column="resource_ids"/>
        <result property="clientSecret" column="client_secret"/>
        <result property="scope" column="scope"/>
        <result property="authorizedGrantTypes" column="authorized_grant_types"/>
        <result property="webServerRedirectUri" column="web_server_redirect_uri"/>
        <result property="authorities" column="authorities"/>
        <result property="accessTokenValidity" column="access_token_validity"/>
        <result property="refreshTokenValidity" column="refresh_token_validity"/>
        <result property="additionalInformation" column="additional_information"/>
        <result property="autoapprove" column="autoapprove"/>
    </resultMap>
</mapper>
