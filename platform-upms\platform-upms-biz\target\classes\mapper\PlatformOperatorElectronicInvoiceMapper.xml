<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.platform.platform.admin.mapper.PlatformOperatorElectronicInvoiceMapper">
    <resultMap id="baseInvoiceInfo"
               type="com.platform.platform.admin.api.entity.PlatformOperatorElectronicInvoiceEntity">
        <id column="id" property="id"/>
        <result column="name" property="name"/>
        <result column="limit_flag" property="limitFlag"/>
        <result column="limit_count" property="limitCount"/>
        <result column="identity" property="identity"/>
        <result column="drawer" property="drawer"/>
        <result column="reviewer" property="reviewer"/>
        <result column="payee" property="payee"/>
        <result column="bank_name" property="bankName"/>
        <result column="bank_account" property="bankAccount"/>
        <result column="phone" property="phone"/>
        <result column="address" property="address"/>
        <result column="tax_num" property="taxNum"/>
        <result column="del_flag" property="delFlag"/>
        <result column="create_by" property="createBy"/>
        <result column="create_time" property="createTime"/>
        <result column="update_by" property="updateBy"/>
        <result column="update_time" property="updateTime"/>
    </resultMap>

    <sql id="invoiceInfo">
        id
        ,
    name,
    limit_flag,
    limit_count,
    identity,
    drawer,
    reviewer,
    payee,
    bank_name,
    bank_account,
    phone,
    address,
    tax_num,
    del_flag,
    create_by,
    create_time,
    update_by,
    update_time
    </sql>

    <select id="getInfo" resultMap="baseInvoiceInfo">
        select
        <include refid="invoiceInfo"/>
        from t_operator_e_invoice
        where
        id = #{id}
        and del_flag = 0
    </select>

    <select id="getList" resultMap="baseInvoiceInfo">
        select
        <include refid="invoiceInfo"/>
        from t_operator_e_invoice
        <where>
            <if test="query.name != null and query.name != ''">
                and name like CONCAT('%',#{query.name},'%')
            </if>
            and del_flag = 0
        </where>
    </select>
</mapper>
