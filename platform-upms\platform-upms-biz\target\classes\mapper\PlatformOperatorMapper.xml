<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.platform.platform.admin.mapper.PlatformOperatorMapper">

    <resultMap id="baseOperatorMap" type="com.platform.platform.admin.api.entity.PlatformOperatorEntity">
        <id column="id" property="id"/>
        <result column="uuid" property="uuid"/>
        <result column="operator_name" property="operatorName"/>
        <result column="contact_person" property="contactPerson"/>
        <result column="contact_phone" property="contactPhone"/>
        <result column="service_phone" property="servicePhone"/>
        <result column="address" property="address"/>
        <result column="url" property="url"/>
        <result column="postal_code" property="postalCode"/>
        <result column="del_flag" property="delFlag"/>
        <result column="remark" property="remark"/>
        <result column="create_by" property="createBy"/>
        <result column="create_time" property="createTime"/>
        <result column="update_by" property="updateBy"/>
        <result column="update_time" property="updateTime"/>
        <result column="mail_id" property="mailId"/>
        <result column="mail_name" property="mailName"/>
        <result column="pay_id" property="payId"/>
        <result column="pay_name" property="payName"/>
        <result column="pay_uuid" property="payUuid"/>
        <result column="last_change_time" property="lastChangeTime"/>
        <result column="currency" property="currency"/>
        <result column="sub_status" property="subStatus"/>
        <result column="sub_expire_time" property="subExpireTime"/>
    </resultMap>

    <resultMap id="operatorSubMap" type="com.platform.platform.admin.api.vo.PlatformOperatorSubInfoVo">
        <id column="id" property="id"/>
        <result column="uuid" property="uuid"/>
        <result column="operator_name" property="operatorName"/>
        <result column="contact_person" property="contactPerson"/>
        <result column="contact_phone" property="contactPhone"/>
        <result column="service_phone" property="servicePhone"/>
        <result column="address" property="address"/>
        <result column="url" property="url"/>
        <result column="postal_code" property="postalCode"/>
        <result column="del_flag" property="delFlag"/>
        <result column="remark" property="remark"/>
        <result column="create_by" property="createBy"/>
        <result column="create_time" property="createTime"/>
        <result column="update_by" property="updateBy"/>
        <result column="update_time" property="updateTime"/>
        <result column="mail_id" property="mailId"/>
        <result column="mail_name" property="mailName"/>
        <result column="pay_id" property="payId"/>
        <result column="pay_name" property="payName"/>
        <result column="pay_uuid" property="payUuid"/>
        <result column="last_change_time" property="lastChangeTime"/>
        <result column="currency" property="currency"/>
        <result column="sub_status" property="subStatus"/>
        <result column="sub_expire_time" property="subExpireTime"/>
        <result column="feature_package_type" property="featurePackageType"/>
        <result column="plan_name" property="planName"/>
        <result column="remain_day" property="remainDay"/>
    </resultMap>

    <sql id="allOperatorInfo">
        t1.id,
        t1.uuid,
        t1.operator_name,
        t1.contact_mail,
        t1.contact_person,
        t1.contact_phone,
        t1.service_phone,
        t1.address,
        t1.url,
        t1.postal_code,
        t1.del_flag,
        t1.remark,
        t1.create_by,
        t1.create_time,
        t1.update_by,
        t1.update_time,
        t1.pay_id,
        t1.pay_uuid,
        t3.pay_name,
        t1.mail_id,
        t4.user_name as mail_name,
        t1.last_change_time,
        t1.currency,
        t1.sub_status,
        t1.sub_expire_time
    </sql>

    <sql id="operatorInfoWithSub">
        t1.id,
        t1.uuid,
        t1.operator_name,
        t1.contact_mail,
        t1.contact_person,
        t1.contact_phone,
        t1.service_phone,
        t1.address,
        t1.url,
        t1.postal_code,
        t1.del_flag,
        t1.remark,
        t1.create_by,
        t1.create_time,
        t1.update_by,
        t1.update_time,
        t1.pay_id,
        t1.pay_uuid,
        t3.pay_name,
        t1.mail_id,
        t4.user_name as mail_name,
        t1.last_change_time,
        t1.currency,
        t1.sub_status,
        t1.sub_expire_time
    </sql>

    <sql id="baseOperatorInfo">
        id,
        uuid,
        operator_name,
        contact_person,
        contact_phone,
        service_phone,
        address,
        url,
        postal_code,
        del_flag,
        remark,
        create_by,
        create_time,
        update_by,
        update_time,
        mail_id,
        pay_id,
        pay_uuid,
        currency
    </sql>

    <sql id="simpleOperatorInfo">
        p.id,
        p.operator_name,
        p.currency
    </sql>

    <select id="getOperatorList" resultMap="operatorSubMap">
        SELECT
        <include refid="operatorInfoWithSub"/>
        FROM
        t_operator t1
        LEFT JOIN t_operator_pay_channel t3 ON t1.pay_id = t3.id
        left join sys_email t4 on t1.mail_id = t4.id
        <where>
            <if test="query.operatorName != null and query.operatorName != ''">
                and t1.operator_name like CONCAT('%',#{query.operatorName},'%')
            </if>
            <if test="query.payName != null and query.payName != ''">
                and t3.pay_name like CONCAT('%',#{query.payName},'%')
            </if>
            <if test="query.agentId != null">
                and t1.id = #{query.agentId}
            </if>
            <if test="query.agentIds != null and query.agentIds.size() != 0">
                and t1.id in
                <foreach item="item" index="index" collection="query.agentIds" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="query.subStatus != null">
                and t1.sub_status = #{query.subStatus}
            </if>
            and t1.del_flag = 0
        </where>
        GROUP BY t1.id
        order by t1.id desc
    </select>

    <select id="getOperatorInfo" resultMap="baseOperatorMap">
        SELECT
        <include refid="allOperatorInfo"/>
        FROM
        t_operator t1
        LEFT JOIN t_operator_pay_channel t3 ON t1.pay_id = t3.id
        left join sys_email t4 on t1.mail_id = t4.id
        where t1.id = #{id}
        and t1.del_flag = 0
    </select>

    <select id="getOperatorByPayUuid" resultType="java.lang.Long">
        select distinct id
        from t_operator
        where uuid = #{payUuid}
          and del_flag = 0
    </select>

    <select id="getOperatorInfoByName" resultMap="baseOperatorMap">
        SELECT
        <include refid="baseOperatorInfo"/>
        FROM t_operator
        WHERE operator_name like CONCAT('%', #{name}, '%')
          and del_flag = 0
    </select>

    <select id="getOperatorIdList" resultType="java.lang.Long">
        SELECT DISTINCT id
        FROM t_operator
        WHERE del_flag = 0
          AND disable_flag = 0
    </select>

    <select id="getMasterOperatorByPayUuid" resultMap="baseOperatorMap">
        SELECT <include refid="baseOperatorInfo"/>
        FROM t_operator
        WHERE uuid = #{payUuid}
        and del_flag = 0
    </select>
    <select id="getOperatorListByMailId" resultMap="baseOperatorMap">
        SELECT
        <include refid="baseOperatorInfo"/>
        FROM t_operator
        WHERE mail_id = #{mailId}
        and del_flag = 0
    </select>

    <select id="getParentOperator" resultMap="baseOperatorMap">
        SELECT
        <include refid="simpleOperatorInfo"/>
        FROM t_operator_relation t
        JOIN t_operator p
        ON p.id = t.ancestor
        WHERE t.descendant = #{id}
        AND p.del_flag = 0
        order by t.ancestor
        LIMIT 1
    </select>

    <select id="getPayPrimaryOperatorCount" resultType="java.lang.Long">
        SELECT
          COUNT(0)
        FROM t_operator p
          JOIN (SELECT DISTINCT
                  ancestor AS id
                FROM t_operator_relation
                GROUP BY descendant
                HAVING COUNT(descendant) = 1) AS t
            ON p.id = t.id
        <where>
            <if test="payId != null">
                and p.pay_id = #{payId}
            </if>
            and p.del_flag = 0
        </where>
    </select>

    <select id="getSecondaryOperatorCount" resultType="java.lang.Long">
       SELECT
          COUNT(0)
        FROM t_operator_relation t
          JOIN t_operator p
            ON p.id = t.descendant
        WHERE t.ancestor = #{id}
            AND p.id != #{id}
            AND p.del_flag = 0
    </select>


    <select id="getSimpleOperatorList" resultMap="baseOperatorMap">
        SELECT
        <include refid="simpleOperatorInfo"/>
        FROM
        t_operator p
        <where>
            <if test="query.operatorName != null and query.operatorName != ''">
                and p.operator_name like CONCAT('%',#{query.operatorName},'%')
            </if>
            <if test="query.agentId != null">
                and p.id = #{query.agentId}
            </if>
            <if test="query.agentIds != null and query.agentIds.size() != 0">
                and p.id in
                <foreach item="item" index="index" collection="query.agentIds" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            and p.del_flag = 0
        </where>
        order by p.id desc
    </select>

    <select id="getPrimaryOperatorList" resultMap="baseOperatorMap">
        SELECT
        <include refid="simpleOperatorInfo"/>
        FROM t_operator p
        JOIN (SELECT DISTINCT
              ancestor AS id
              FROM t_operator_relation
              GROUP BY descendant
              HAVING COUNT(descendant) = 1) AS t
            ON p.id = t.id
        <where>
            <if test="name != null and name != ''">
                and p.operator_name like CONCAT('%',#{name},'%')
            </if>
            and p.del_flag = 0
            ORDER BY p.id
        </where>
    </select>

    <resultMap id="baseOperatorTree" type="com.platform.platform.admin.api.entity.PlatformOperatorTreeEntity">
        <id column="f_id" property="id"/>
        <result column="f_name" property="name"/>
        <collection property="children" ofType="com.platform.platform.admin.api.entity.PlatformOperatorTreeEntity">
            <id column="s_id" property="id"/>
            <result column="s_name" property="name"/>
        </collection>
    </resultMap>

    <select id="treeByAdmin" resultMap="baseOperatorTree">
        SELECT
        f.id             AS f_id,
        f.operator_name  AS f_name,
        p2.id            AS s_id,
        p2.operator_name AS s_name
        FROM (SELECT DISTINCT
        p.id,
        p.operator_name
        FROM t_operator p
        JOIN t_operator_relation t
        ON p.id = t.ancestor
        AND p.del_flag = 0
        GROUP BY t.descendant
        HAVING COUNT(t.descendant) = 1
        ORDER BY p.id ) AS f
        LEFT JOIN t_operator_relation r
        ON r.ancestor = f.id
        LEFT JOIN t_operator p2
        ON r.descendant = p2.id
        AND r.ancestor != r.descendant
        AND p2.del_flag = 0
        ORDER BY f.id, p2.id
    </select>

    <select id="treeByPrimary" resultMap="baseOperatorTree">
        SELECT
        f.id             AS f_id,
        f.operator_name  AS f_name,
        p2.id            AS s_id,
        p2.operator_name AS s_name
        FROM (SELECT DISTINCT
        p.id,
        p.operator_name
        FROM t_operator p
        JOIN t_operator_relation t
        ON p.id = t.ancestor
        AND p.del_flag = 0
        WHERE p.id = #{agentId} ) AS f
        LEFT JOIN t_operator_relation r
        ON r.ancestor = f.id
        LEFT JOIN t_operator p2
        ON r.descendant = p2.id
        AND r.ancestor != r.descendant
        AND p2.del_flag = 0
        ORDER BY f.id, p2.id
    </select>

    <select id="treeBySecondary" resultMap="baseOperatorTree">
        SELECT
        p1.id            AS f_id,
        p1.operator_name AS f_name,
        p2.id            AS s_id,
        p2.operator_name AS s_name
        FROM t_operator_relation r
        LEFT JOIN t_operator p1
        ON r.ancestor = p1.id
        LEFT JOIN t_operator p2
        ON r.descendant = p2.id
        WHERE r.descendant = #{agentId}
        AND r.ancestor != r.descendant
        AND p1.del_flag = 0
        AND p2.del_flag = 0
    </select>

    <select id="getUuidByAgentId" resultType="java.lang.String">
        SELECT
          uuid
        FROM t_operator p
        WHERE p.id = (SELECT
                        t.ancestor
                      FROM t_operator_relation t
                      WHERE t.descendant = #{agentId}
                      ORDER BY t.ancestor
                      LIMIT 1)
        AND p.del_flag = 0
    </select>
</mapper>
