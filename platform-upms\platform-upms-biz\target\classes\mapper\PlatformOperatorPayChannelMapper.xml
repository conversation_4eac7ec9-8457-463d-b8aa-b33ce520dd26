<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.platform.platform.admin.mapper.PlatformOperatorPayChannelMapper">

    <resultMap id="basePayChannel" type="com.platform.platform.admin.api.entity.PlatformOperatorPayChannelEntity">
        <id column="id" property="id"/>
        <result column="uuid" property="uuid"/>
        <result column="pay_name" property="payName"/>
        <result column="api_key" property="apiKey" typeHandler="com.platform.platform.admin.api.config.ChargerAdminEncryptHandler"/>
        <result column="notify_url" property="notifyUrl"/>
        <result column="firebase" property="firebase"/>
        <result column="google_map" property="googleMap" typeHandler="com.platform.platform.admin.api.config.ChargerAdminEncryptHandler"/>
        <result column="del_flag" property="delFlag"/>
        <result column="create_by" property="createBy"/>
        <result column="create_time" property="createTime"/>
        <result column="update_by" property="updateBy"/>
        <result column="update_time" property="updateTime"/>
    </resultMap>

    <sql id="baseListSql">
        t1.id,
        t1.UUID,
        t1.pay_name,
        t1.notify_url,
        t1.del_flag,
        t1.create_by,
        t1.create_time,
        t1.update_by
    </sql>

    <sql id="payChannelInfo">
        t1.id,
        t1.UUID,
        t1.pay_name,
        t1.api_key,
        t1.notify_url,
        t1.firebase,
        t1.google_map,
        t1.del_flag,
        t1.create_by,
        t1.create_time,
        t1.update_by,
        t1.auto_refund_flag
    </sql>

    <select id="getPayChannelInfo" resultMap="basePayChannel">
        select
        <include refid="payChannelInfo"/>
        from t_operator_pay_channel t1
        where t1.id = #{id}
        and t1.del_flag = 0
    </select>

    <select id="getPayChannelListByAgent" resultMap="basePayChannel">
        select
        <include refid="baseListSql"/>
        from t_operator_pay_channel t1
        LEFT JOIN t_operator t3 ON t3.pay_id = t1.id
        <where>
            <if test="query.payName != null and query.payName != ''">
                and t1.pay_name like CONCAT('%',#{query.payName},'%')
            </if>
            and t3.id = #{query.agentId}
            and t1.del_flag = 0
            and t3.del_flag = 0
        </where>
        union
        select
        <include refid="baseListSql"/>
        from t_operator_pay_channel t1
        <where>
            <if test="query.payName != null and query.payName != ''">
                and t1.pay_name like CONCAT('%',#{query.payName},'%')
            </if>
            <if test="query.createName != null and query.createName.size() != 0">
                and t1.create_by in
                <foreach item="item" index="index" collection="query.createName" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            and t1.del_flag = 0
        </where>
        order by id desc
    </select>

    <select id="getPayChannelListBySystem" resultMap="basePayChannel">
        select
        <include refid="baseListSql"/>
        from t_operator_pay_channel t1
        <where>
            <if test="payName != null and payName != ''">
                and t1.pay_name like CONCAT('%',#{payName},'%')
            </if>
            and t1.del_flag = 0
        </where>
    </select>
    <select id="getInfoByAgentId" resultMap="basePayChannel">
        select
        <include refid="payChannelInfo"/>
        from t_operator_pay_channel t1
        RIGHT JOIN t_operator t2 ON t1.id = t2.pay_id
        WHERE
        t2.id = #{id}
    </select>
    <select id="getInfoByUuid" resultMap="basePayChannel">
        select
            <include refid="payChannelInfo"/>
        from t_operator_pay_channel t1
        JOIN t_operator t2
        ON t2.pay_id = t1.id
        where t2.uuid = #{uuid}
        and t2.del_flag = 0
        and t1.del_flag = 0
    </select>
</mapper>
