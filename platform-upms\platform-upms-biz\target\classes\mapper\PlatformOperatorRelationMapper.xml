<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.platform.platform.admin.mapper.PlatformOperatorRelationMapper">
    <select id="getOperatorIdList" resultType="java.lang.Long">
        select descendant
        from t_operator_relation
        where ancestor = #{id}
    </select>
    <select id="getAgentIdsByUuid" resultType="java.lang.Long">
        SELECT DISTINCT
            descendant
        FROM
            t_operator_relation
        WHERE
            ancestor = ( SELECT id FROM t_operator WHERE uuid = #{payUuid} LIMIT 1 )
    </select>
</mapper>
