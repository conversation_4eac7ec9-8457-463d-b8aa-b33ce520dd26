<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.platform.platform.admin.mapper.PlatformParameterMapper">
    <resultMap id="baseInfo" type="com.platform.platform.admin.api.entity.PlatformParameterEntity">
        <id column="id" property="id"/>
        <result column="setting_type" property="settingType"/>
        <result column="setting_code" property="settingCode"/>
        <result column="setting_name" property="settingName"/>
        <result column="setting_value" property="settingValue"/>
        <result column="setting_desc" property="settingDesc"/>
        <result column="del_flag" property="delFlag"/>
        <result column="create_by" property="createBy"/>
        <result column="create_time" property="createTime"/>
        <result column="update_by" property="updateBy"/>
        <result column="update_time" property="updateTime"/>
    </resultMap>

    <sql id="baseSqlInfo">
        id,
        setting_type,
        setting_code,
        setting_name,
        setting_value,
        setting_desc,
        create_by,
        create_time,
        update_by,
        update_time
    </sql>
    <select id="getParamList" resultMap="baseInfo">
        select
            <include refid="baseSqlInfo"/>
        from sys_parameter
        <where>
            <if test="query.settingType != null and query.settingType != ''">
                and setting_type like CONCAT('%',#{query.settingType},'%')
            </if>
            <if test="query.settingCode != null and query.settingCode != ''">
                and setting_code like CONCAT ('%',#{query.settingCode},'%')
            </if>
            <if test="query.settingName != null and query.settingName != ''">
                and setting_name like CONCAT ('%',#{query.settingName},'%')
            </if>
            <if test="query.settingValue != null and query.settingValue != ''">
                and setting_value like CONCAT ('%',#{query.settingValue},'%')
            </if>
            <if test="query.settingDesc != null and query.settingDesc != ''">
                and setting_desc like CONCAT ('%',#{query.settingDesc},'%')
            </if>
            and del_flag = 0
            order by id asc
        </where>
    </select>

</mapper>
