<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.platform.platform.admin.mapper.PlatformPayStatusMapper">
    <resultMap id="baseMapInfo" type="com.platform.platform.admin.entity.PlatformPayStatusEntity">
        <id column="id" property="id"/>
        <result column="subscribe_id" property="subscribeId"/>
        <result column="agent_id" property="agentId"/>
        <result column="agent_name" property="agentName"/>
        <result column="payment_id" property="paymentId"/>
        <result column="status" property="status"/>
        <result column="order_type" property="orderType"/>
        <result column="order_no" property="orderNo"/>
        <result column="refund_amount" property="refundAmount"/>
        <result column="capture_amount" property="captureAmount"/>
        <result column="currency" property="currency"/>
        <result column="pay_uuid" property="payUuid"/>
        <result column="remark" property="remark"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
    </resultMap>
    <sql id="baseSqlInfo">
        id,
        subscribe_id,
        agent_id,
        agent_name,
        payment_id,
        status,
        order_type,
        order_no,
        refund_amount,
        capture_amount,
        currency,
        pay_uuid,
        remark,
        create_time,
        update_time
    </sql>

    <select id="getInfo" resultMap="baseMapInfo">
        select
        <include refid="baseSqlInfo"/>
        from t_platform_pay_status
        <where>
            <if test="query.orderNo != null and query.orderNo != ''">
                and order_no =  #{query.orderNo}
            </if>
            <if test="query.paymentId != null and query.paymentId != ''">
                and payment_id =  #{query.paymentId}
            </if>
            <if test="query.agentId != null">
                and agent_id =  #{query.agentId}
            </if>
        </where>
        limit 1
    </select>
</mapper>