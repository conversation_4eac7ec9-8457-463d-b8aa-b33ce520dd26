<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.platform.platform.admin.mapper.PlatformPlanMapper">
    <resultMap id="baseMapInfo" type="com.platform.platform.admin.entity.PlatformPlanEntity">
        <id column="id" property="id"/>
        <result column="plan_no" property="planNo"/>
        <result column="plan_name" property="planName"/>
        <result column="plan_type" property="planType"/>
        <result column="feature_package_type" property="featurePackageType"/>
        <result column="ac_connector_number" property="acConnectorNumber"/>
        <result column="ac_connector_price" property="acConnectorPrice"/>
        <result column="ac_transaction_fee" property="acTransactionFee"/>
        <result column="dc_connector_number" property="dcConnectorNumber"/>
        <result column="dc_connector_price" property="dcConnectorPrice"/>
        <result column="dc_transaction_fee" property="dcTransactionFee"/>
        <result column="valid_days" property="validDays"/>
        <result column="use_flag" property="useFlag"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="create_by" property="createBy"/>
        <result column="update_by" property="updateBy"/>
    </resultMap>

    <resultMap id="webMap" type="com.platform.platform.admin.entity.PlatformPlanEntity">
        <id column="id" property="id"/>
        <result column="feature_package_type" property="featurePackageType"/>
        <result column="ac_connector_price" property="acConnectorPrice"/>
        <result column="dc_connector_price" property="dcConnectorPrice"/>
    </resultMap>

    <sql id="baseSql">
        id,
        plan_no,
        plan_name,
        plan_type,
        feature_package_type,
        ac_connector_number,
        ac_connector_price,
        ac_transaction_fee,
        dc_connector_number,
        dc_connector_price,
        dc_transaction_fee,
        valid_days,
        use_flag,
        create_time,
        update_time,
        create_by,
        update_by
    </sql>

    <sql id="webSql">
        id,
        feature_package_type,
        ac_connector_price,
        dc_connector_price
    </sql>

    <select id="getInfoById" resultMap="baseMapInfo">
        select
        <include refid="baseSql"/>
        from t_platform_plan where id = #{id}
    </select>

    <select id="list" resultMap="baseMapInfo">
        select
        <include refid="baseSql"/>
        from t_platform_plan
        <where>
            <if test="query.planName != null and query.planName !=''">
                and plan_name like concat(#{query.planName}, '%')
            </if>
            <if test="query.list != null and query.list.size() > 0">
                and create_by in
                <foreach collection="query.list" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </where>
        order by create_time desc
    </select>

    <select id="universalList" resultMap="webMap">
      select
      <include refid="webSql"/>
      from t_platform_plan where plan_type = 1
    </select>

    <select id="getInfoByNo" resultMap="baseMapInfo">
        select
        <include refid="baseSql"/>
        from t_platform_plan where plan_no = #{planNo} and plan_type = 0
    </select>

    <select id="getInfoByType" resultMap="baseMapInfo">
        select
        <include refid="baseSql"/>
        from t_platform_plan where plan_type = 1 and feature_package_type = #{featurePackageType}
    </select>

</mapper>