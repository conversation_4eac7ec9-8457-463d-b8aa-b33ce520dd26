<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.platform.platform.admin.mapper.PlatformRoleMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.platform.platform.admin.api.entity.PlatformRoleEntity">
        <id column="role_id" property="roleId"/>
        <result column="role_name" property="roleName"/>
        <result column="role_code" property="roleCode"/>
        <result column="role_desc" property="roleDesc"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="del_flag" property="delFlag"/>
        <result column="create_by" property="createBy"/>
        <result column="update_by" property="updateBy"/>
        <result column="role_flag" property="roleFlag"/>
        <result column="role_type" property="roleType"/>
    </resultMap>

    <sql id="baseSql">
        role_id,
        role_name,
        role_code,
        role_desc,
        create_time,
        create_by,
        update_time,
        update_by,
        del_flag,
        role_flag,
        role_type
    </sql>

    <!-- 通过用户ID，查询角色信息-->
    <select id="listRolesByUserId" resultMap="BaseResultMap">
        SELECT r.*
        FROM sys_role r,
             sys_user_role ur
        WHERE r.role_id = ur.role_id
          AND r.del_flag = 0
          and ur.user_id IN (#{userId})
    </select>

    <select id="getRolePage" resultMap="BaseResultMap">
        select
        <include refid="baseSql"/>
        from sys_role
        <where>
            <if test="query.roleName != null and query.roleName != ''">
                and role_name LIKE CONCAT('%',#{query.roleName},'%')
            </if>
            <if test="query.roleFlag != null">
                and role_flag = #{query.roleFlag}
            </if>
            <if test="query.roleType != null">
                and role_type = #{query.roleType}
            </if>
            <if test="list != null and list.size() > 0">
                and create_by in
                <foreach collection="list" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            and del_flag = 0
        </where>
    </select>
    
    <select id="getRoleIdByAgentId" resultType="java.lang.Integer">
         SELECT
          r.role_id
        FROM sys_role r
          JOIN sys_user u
            ON r.create_by = u.username
          JOIN t_operator_relation p
            ON u.agent_id = p.descendant
        WHERE p.ancestor = #{agentId}
          AND r.del_flag = 0
    </select>

    <select id="getRoleByUserId" resultMap="BaseResultMap">
        SELECT r.*
        FROM sys_role r,
             sys_user_role ur
        WHERE r.role_id = ur.role_id
          AND r.del_flag = 0
          and ur.user_id =#{userId}
        limit 1
    </select>


    <select id="getSubRolePage" resultMap="BaseResultMap">
        select
        <include refid="baseSql"/>
        from sys_role
        <where>
            <if test="roleName != null and roleName != ''">
                and role_name LIKE CONCAT('%',#{roleName},'%')
            </if>
            and role_flag = 0
            and role_type > 0
            and del_flag = 0
        </where>
    </select>
</mapper>
