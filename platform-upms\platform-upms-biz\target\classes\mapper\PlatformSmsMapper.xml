<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.platform.platform.admin.mapper.PlatformSmsMapper">

    <resultMap id="smsList" type="com.platform.platform.admin.api.entity.PlatformSmsEntity">
        <id column="id" property="id"/>
        <result column="sms_name" property="smsName"/>
        <result column="type" property="type"/>
        <result column="access_key_id" property="accessKeyId"/>
        <result column="access_key_secret" property="accessKeySecret"/>
        <result column="sign_name" property="signName"/>
        <result column="captcha_template_code" property="captchaTemplateCode"/>
        <result column="alarm_template_code" property="alarmTemplateCode"/>
        <result column="update_phone_template_code" property="updatePhoneTemplateCode"/>
        <result column="reset_password_template_code" property="resetPasswordTemplateCode"/>
        <result column="company_fund_warning_template_code" property="companyFundWarningTemplateCode"/>
        <result column="company_fund_disable_template_code" property="companyFundDisableTemplateCode"/>
        <result column="del_flag" property="delFlag"/>
        <result column="create_by" property="createBy"/>
        <result column="create_time" property="createTime"/>
        <result column="update_by" property="updateBy"/>
        <result column="update_time" property="updateTime"/>
    </resultMap>

    <sql id="smsInfo">
        t1
        .
        id
        ,
    t1.sms_name,
    t1.type,
    t1.access_key_id,
    t1.access_key_secret,
    t1.sign_name,
    t1.captcha_template_code,
    t1.alarm_template_code,
    t1.update_phone_template_code,
    t1.reset_password_template_code,
    t1.company_fund_warning_template_code,
	t1.company_fund_disable_template_code,
    t1.del_flag,
    t1.create_by,
    t1.create_time,
    t1.update_by,
    t1.update_time
    </sql>

    <!--根据运营商ID查询运营商的短信模版-->
    <select id="getListByAgentId" parameterType="java.lang.Long"
            resultType="com.platform.platform.admin.api.entity.PlatformSmsEntity">
        SELECT a.*
        FROM `sys_aliyun_sms` a
                 INNER JOIN `t_operator` b
                            ON a.`id` = b.`sms_id`
                                and b.del_flag = 0
                                and a.del_flag = b.del_flag
                                and b.disable_flag = 0
        where b.agent_id = #{id}
    </select>


    <!--&lt;!&ndash;根据appid查询短信通道&ndash;&gt;-->
    <!--<select id="getSmsByAppId" resultType="com.platform.platform.admin.api.entity.PlatformSmsEntitymsEntity"  parameterType="java.lang.String">-->
    <!--SELECT  c.* FROM `t_operator` a-->
    <!--INNER JOIN `sys_aliyun_sms` c-->
    <!--ON a.`sms_id` = c.`id`-->
    <!--and c.del_flag = 0-->
    <!--and a.del_flag = c.del_flag-->
    <!--and a.disable_flag = 0-->
    <!--where a.wx_appid = #{appid}-->
    <!--limit 1-->
    <!--</select>-->


    <!--根据payUuid查询短信通道-->
    <select id="getSmsByPayUuid" resultType="com.platform.platform.admin.api.entity.PlatformSmsEntity"
            parameterType="java.lang.String">
        SELECT c.*
        FROM `t_operator` a
                 INNER JOIN `sys_aliyun_sms` c
                            ON a.`sms_id` = c.`id`
                                and c.del_flag = 0
                                and a.del_flag = c.del_flag
                                and a.disable_flag = 0
        where a.uuid = #{payUuid} limit 1
    </select>

    <select id="getSmsListByAgent" resultMap="smsList">
        select
        <include refid="smsInfo"/>
        from sys_aliyun_sms t1
        LEFT JOIN t_operator t2 ON t2.sms_id = t1.id
        <where>
            <if test="query.smsName != null and query.smsName != ''">
                t1.sms_name like CONCAT('%',#{query.smsName},'%')
            </if>
            and t2.id = #{query.agentId}
            AND t1.del_flag = 0
            AND t2.del_flag = 0
        </where>
        union all
        select
        <include refid="smsInfo"/>
        from sys_aliyun_sms t1
        <where>
            <if test="query.smsName != null and query.smsName != ''">
                t1.sms_name like CONCAT('%',#{query.smsName},'%')
            </if>
            <if test="query.createName != null and query.createName.size() != 0">
                and t1.create_by in
                <foreach item="item" index="index" collection="query.createName" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            AND t1.del_flag = 0
        </where>
    </select>
    <select id="getSmsListBySystem" resultMap="smsList">
        select
        <include refid="smsInfo"/>
        from sys_aliyun_sms t1
        <where>
            <if test="smsName != null and smsName != ''">
                t1.sms_name like CONCAT('%',#{smsName},'%')
            </if>
            AND t1.del_flag = 0
        </where>
    </select>
</mapper>
