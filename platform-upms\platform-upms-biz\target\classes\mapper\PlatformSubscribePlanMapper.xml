<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.platform.platform.admin.mapper.PlatformSubscribePlanMapper">
    <resultMap id="baseMap" type="com.platform.platform.admin.entity.PlatformSubscribePlanEntity">
        <id column="id" property="id"/>
        <result column="agent_id" property="agentId"/>
        <result column="agent_name" property="agentName"/>
        <result column="plan_no" property="planNo"/>
        <result column="plan_name" property="planName"/>
        <result column="plan_type" property="planType"/>
        <result column="feature_package_type" property="featurePackageType"/>
        <result column="ac_connector_number" property="acConnectorNumber"/>
        <result column="ac_connector_price" property="acConnectorPrice"/>
        <result column="ac_transaction_fee" property="acTransactionFee"/>
        <result column="dc_connector_number" property="dcConnectorNumber"/>
        <result column="dc_connector_price" property="dcConnectorPrice"/>
        <result column="dc_transaction_fee" property="dcTransactionFee"/>
        <result column="valid_days" property="validDays"/>
        <result column="total_fee" property="totalFee"/>
        <result column="paid_fee" property="paidFee"/>
        <result column="to_pay_fee" property="toPayFee"/>
        <result column="create_time" property="createTime"/>
        <result column="pay_time" property="payTime"/>
        <result column="create_by" property="createBy"/>
        <result column="expire_time" property="expireTime"/>
        <result column="invalid_time" property="invalidTime"/>
        <result column="remain_day" property="remainDay"/>
        <result column="expire_flag" property="expireFlag"/>
        <result column="pay_status" property="payStatus"/>
    </resultMap>

    <sql id="baseSql">
        id,
        agent_id,
        agent_name,
        plan_no,
        plan_name,
        plan_type,
        feature_package_type,
        ac_connector_number,
        ac_connector_price,
        ac_transaction_fee,
        dc_connector_number,
        dc_connector_price,
        dc_transaction_fee,
        valid_days,
        total_fee,
        paid_fee,
        to_pay_fee,
        create_time,
        pay_time,
        create_by,
        expire_time,
        invalid_time,
        remain_day,
        expire_flag,
        pay_status
    </sql>

    <select id="getSubAgentIdList" resultType="long">
        select distinct agent_id from t_platform_subscribe_plan where pay_status = 1 and expire_flag = 0
    </select>

    <select id="getSubscribeListByAgentId" resultMap="baseMap">
        select
        <include refid="baseSql"/>
        from t_platform_subscribe_plan where pay_status = 1 and expire_flag = 0 and agent_id = #{agentId}
    </select>

    <select id="getFeaturePackageTypeByAgentId" resultType="int">
        select feature_package_type from t_platform_subscribe_plan where agent_id = #{agentId} and pay_status = 1 and expire_flag = 0 order by invalid_time asc limit 1
    </select>

    <select id="getSubList" resultMap="baseMap">
        SELECT
          p.id,
          r.descendant as  agent_id,
          p.feature_package_type,
          p.plan_name,
          p.remain_day
        FROM t_operator_relation r
          JOIN t_platform_subscribe_plan p
            ON p.agent_id = r.ancestor
              AND p.expire_flag = 0
              AND p.pay_status = 1
        WHERE r.descendant in
        <foreach item="item" index="index" collection="agentIds" open="(" separator="," close=")">
            #{item}
        </foreach>
        GROUP BY r.descendant
    </select>
</mapper>