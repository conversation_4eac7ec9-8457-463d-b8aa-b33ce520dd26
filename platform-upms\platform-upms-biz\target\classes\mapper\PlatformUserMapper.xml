<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.platform.platform.admin.mapper.PlatformUserMapper">
    <resultMap id="baseMapInfo" type="com.platform.platform.admin.api.entity.PlatformUserEntity">
        <id column="user_id" property="userId"/>
        <result column="username" property="username"/>
        <result column="password" property="password"/>
        <result column="phone" property="phone"/>
        <result column="email" property="email"/>
        <result column="agent_id" property="agentId"/>
        <result column="agent_Name" property="agentName"/>
        <result column="currency" property="currency"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="lock_flag" property="lockFlag"/>
        <result column="del_flag" property="delFlag"/>
        <result column="create_by" property="createBy"/>
        <result column="update_by" property="updateBy"/>
        <result column="sub_status" property="subStatus"/>
        <result column="expire_remind_days" property="expireRemindDays"/>
    </resultMap>

    <!-- userVo结果集 -->
    <resultMap id="userVoResultMap" type="com.platform.platform.admin.api.vo.UserVO">
        <id column="user_id" property="userId"/>
        <result column="username" property="username"/>
        <result column="password" property="password"/>
        <result column="phone" property="phone"/>
        <result column="email" property="email"/>
        <result column="agent_id" property="agentId"/>
        <result column="agent_name" property="agentName"/>
        <result column="currency" property="currency"/>
        <result column="create_by" property="createBy"/>
        <result column="ucreate_time" property="createTime"/>
        <result column="update_by" property="updateBy"/>
        <result column="uupdate_time" property="updateTime"/>
        <result column="lock_flag" property="lockFlag"/>
        <result column="udel_flag" property="delFlag"/>
        <collection property="roleList" ofType="com.platform.platform.admin.api.entity.PlatformRoleEntity">
            <id column="role_id" property="roleId"/>
            <result column="role_name" property="roleName"/>
            <result column="role_code" property="roleCode"/>
            <result column="role_desc" property="roleDesc"/>
            <result column="rcreate_time" property="createTime"/>
            <result column="rupdate_time" property="updateTime"/>
        </collection>
    </resultMap>

    <sql id="userRoleSql">
        `user`.user_id,
        `user`.username,
		`user`.`password`,
		`user`.phone,
		`user`.email,
		`user`.create_time AS ucreate_time,
		`user`.update_time AS uupdate_time,
		`user`.del_flag AS udel_flag,
		`user`.lock_flag AS lock_flag,
		r.role_id,
		r.role_name,
		r.role_code,
		r.role_desc,
		r.create_time AS rcreate_time,
		r.update_time AS rupdate_time
    </sql>

    <sql id="userRoleDeptSql">
        `user`.user_id,
		`user`.username,
		`user`.`password`,
		`user`.phone,
		`user`.email,
		`user`.agent_id,
		t.operator_name as agent_name,
		t.currency,
		`user`.create_by,
		`user`.create_time AS ucreate_time,
		`user`.update_by,
		`user`.update_time AS uupdate_time,
		`user`.del_flag AS udel_flag,
		`user`.lock_flag AS lock_flag,
		r.role_id,
		r.role_name,
		r.role_code,
		r.role_desc,
		r.create_time AS rcreate_time,
		r.update_time AS rupdate_time
    </sql>

    <sql id="userInfo">
        t1.user_id,
        t1.username,
        t1.password,
        t1.phone,
        t1.lock_flag,
        t1.del_flag,
        t1.email,
        t1.agent_id,
        t2.operator_name as agent_name,
        t2.currency,
        t2.sub_status,
        t2.expire_remind_days,
        t1.create_time,
        t1.create_by,
        t1.update_time,
        t1.update_by
    </sql>

    <sql id="simpleUserInfo">
        u.user_id,
        u.username
    </sql>

    <select id="getUserVoById" resultMap="userVoResultMap">
        SELECT
        <include refid="userRoleDeptSql"/>
        FROM
        sys_user AS `user`
        LEFT JOIN sys_user_role AS ur ON ur.user_id = `user`.user_id
        LEFT JOIN sys_role AS r ON r.role_id = ur.role_id
        LEFT JOIN t_operator t ON `user`.agent_id = t.id
        WHERE
        `user`.user_id = #{id}
        and `user`.del_flag = 0
    </select>

    <select id="getUserVosPage" resultMap="userVoResultMap">
        SELECT
        <include refid="userRoleDeptSql"/>
        FROM
        sys_user AS `user`
        LEFT JOIN sys_user_role AS ur ON ur.user_id = `user`.user_id
        LEFT JOIN sys_role AS r ON r.role_id = ur.role_id
        LEFT JOIN t_operator t ON `user`.agent_id = t.id
        <where>
            <if test="query.userName != null and query.userName != ''">
                and `user`.username LIKE CONCAT('%',#{query.userName},'%')
            </if>
            <if test="query.agentName != null and query.agentName != ''">
                and t.operator_name LIKE CONCAT('%',#{query.agentName},'%')
            </if>
            <if test="query.phone != null and query.phone != ''">
                and `user`.phone LIKE CONCAT('%',#{query.phone},'%')
            </if>
            <if test="query.roleName != null and query.roleName != ''">
                and r.role_name LIKE CONCAT('%',#{query.roleName},'%')
            </if>
            <if test="query.list != null and query.list.size() > 0">
                and `user`.username in
                <foreach collection="query.list" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            and `user`.del_flag = 0
        </where>
        ORDER BY `user`.create_time DESC
    </select>

    <select id="getUserInfo" resultMap="baseMapInfo">
        select
        <include refid="userInfo"/>
        from sys_user t1
        left join t_operator t2
        on t1.agent_id = t2.id
        and t2.del_flag = 0
        where t1.del_flag = 0
        and t1.username = #{username}
    </select>

    <select id="getCountByUserName" resultType="java.lang.Integer">
        select
        count(1)
        from sys_user
        <where>
            username = #{userName}
            <if test="id != null">
                and user_id != #{id}
            </if>
            and del_flag = 0
        </where>
    </select>

    <select id="getListByAgentId" resultMap="baseMapInfo">
        select
        <include refid="userInfo"/>
        from sys_user t1
        left join t_operator t2
        on t1.agent_id = t2.id
        and t2.del_flag = 0
        where t1.del_flag = 0
        and t1.agent_id = #{agentId}
    </select>

    <select id="getChildUserCount" resultType="java.lang.Long">
        SELECT
          COUNT(1)
        FROM sys_user_relation r
          JOIN sys_user u
            ON r.descendant = u.user_id
        WHERE r.ancestor = #{userId}
            AND u.del_flag = 0
    </select>

    <select id="getChildUserList" resultMap="baseMapInfo">
        SELECT
        <include refid="simpleUserInfo"/>
        FROM sys_user_relation r
          JOIN sys_user u
            ON r.descendant = u.user_id
        WHERE r.ancestor = #{userId}
            AND u.del_flag = 0
    </select>

    <select id="getChildUserByAgentId" resultMap="baseMapInfo">
        SELECT
        <include refid="simpleUserInfo"/>
        FROM sys_user u
        JOIN t_operator_relation p
        ON u.agent_id = p.descendant
        WHERE p.ancestor = #{agentId}
        AND u.del_flag = 0
    </select>
</mapper>
