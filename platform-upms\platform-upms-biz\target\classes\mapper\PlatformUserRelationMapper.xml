<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.platform.platform.admin.mapper.PlatformUserRelationMapper">
    <select id="getUserRelation" resultType="java.lang.String">
        SELECT t2.username
        FROM sys_user_relation t1
                 LEFT JOIN sys_user t2 ON t1.descendant = t2.user_id
        WHERE t1.ancestor = #{id}
          AND t2.del_flag = 0
    </select>

    <select id="getUserRelationPage" resultType="java.lang.String">
        SELECT t2.username
        FROM sys_user_relation t1
                 LEFT JOIN sys_user t2 ON t1.descendant = t2.user_id
        WHERE t1.ancestor = #{id}
          AND t2.del_flag = 0
          <if test="userName != null and userName != ''">
              and t2.username like concat('%', #{userName}, '%')
          </if>
    </select>
</mapper>
